import { User } from 'lucide-react';
import moment from 'moment';
import ReactMarkdown from 'react-markdown';

import { vesa } from '@/assets/images';
import { ChatMessage } from '@/types/agents';

const MessageComponent = ({
  message,
  agentAvatar,
}: {
  message: ChatMessage;
  agentAvatar?: string;
}) => {
  const isUser = message.sender === 'user';

  return (
    <div className="mb-6 flex gap-3">
      {/* Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        {isUser ? (
          <div className="flex h-full w-full items-center justify-center rounded-full bg-peachTwo">
            <User className="h-5 w-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-grayTwentySix">
            <img
              src={agentAvatar || vesa}
              alt={message.senderName}
              className="h-full w-full rounded-full object-cover"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {moment(message.timestamp).format('h:mm A')}
          </span>
        </div>

        {/* Message Text */}
        <div className="rounded-lg bg-gray-5 p-3 text-grayTwentyFour">
          <ReactMarkdown
            components={{
              p: props => <p className="mb-2 last:mb-0">{props.children}</p>,
              strong: props => (
                <strong className="font-semibold text-darkGray">
                  {props.children}
                </strong>
              ),
              em: props => (
                <em className="italic text-gray-600">{props.children}</em>
              ),
              code: props => (
                <code className="rounded bg-gray-100 px-1 py-0.5 font-mono text-sm text-gray-800">
                  {props.children}
                </code>
              ),
              pre: props => (
                <pre className="mt-2 overflow-x-auto rounded bg-gray-100 p-2 font-mono text-sm text-gray-800">
                  {props.children}
                </pre>
              ),
              ul: props => (
                <ul className="ml-4 list-disc space-y-1">{props.children}</ul>
              ),
              ol: props => (
                <ol className="ml-4 list-decimal space-y-1">
                  {props.children}
                </ol>
              ),
              li: props => (
                <li className="text-grayTwentyFour">{props.children}</li>
              ),
              a: props => (
                <a
                  href={props.href}
                  className="hover:text-primaryDark text-primary underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {props.children}
                </a>
              ),
            }}
          >
            {message.content}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

export default MessageComponent;
