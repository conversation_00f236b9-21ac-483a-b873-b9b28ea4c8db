export const AgentChatSidebarSkeleton = () => {
  return (
    <div className="relative mx-auto flex h-full w-full max-w-[486px] flex-shrink-0 flex-col bg-gradient-to-br from-orange-50/50 to-orange-100 px-6 py-4">
      {/* Agent Profile Skeleton */}
      <div className="z-10 mb-4 flex animate-pulse items-center justify-center gap-3">
        <div className="h-12 w-12 flex-shrink-0 rounded-full bg-gray-200 p-1"></div>
        <div className="flex flex-col gap-1">
          <div className="h-4 w-20 rounded bg-gray-200"></div>
          <div className="bg-gray-150 h-3 w-24 rounded"></div>
        </div>
      </div>

      {/* Chat Interface Skeleton */}
      <div className="z-10 flex-1 overflow-hidden rounded-2xl bg-white bg-opacity-40">
        <div className="flex h-full flex-col">
          {/* Messages Area Skeleton */}
          <div className="flex-1 overflow-y-auto px-4 py-4">
            {/* Date Header Skeleton */}
            <div className="mb-2 text-center">
              <div className="mx-auto h-3 w-16 animate-pulse rounded bg-gray-200"></div>
            </div>

            {/* Message Skeletons */}
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="mb-6 flex animate-pulse gap-3">
                {/* Avatar Skeleton */}
                <div className="h-10 w-10 flex-shrink-0 rounded-full bg-gray-200"></div>

                {/* Message Content Skeleton */}
                <div className="flex-1">
                  {/* Header Skeleton */}
                  <div className="mb-1 flex items-center gap-2">
                    <div className="h-4 w-16 rounded bg-gray-200"></div>
                    <div className="bg-gray-150 h-3 w-12 rounded"></div>
                  </div>

                  {/* Message Text Skeleton */}
                  <div className="rounded-lg bg-gray-5 p-3">
                    <div className="space-y-2">
                      <div className="h-4 w-full rounded bg-gray-200"></div>
                      <div className="h-4 w-3/4 rounded bg-gray-200"></div>
                      <div className="h-4 w-1/2 rounded bg-gray-200"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Chat Input Skeleton */}
          <div className="flex-shrink-0 px-4 py-4">
            <div className="animate-pulse">
              <div className="h-12 w-full rounded-lg bg-gray-200"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Background Objects */}
      <div className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat opacity-30"></div>
      <div className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat opacity-30"></div>
    </div>
  );
};
