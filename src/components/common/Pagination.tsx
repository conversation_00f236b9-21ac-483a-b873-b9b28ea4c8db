import { ChevronLeft, ChevronRight } from 'lucide-react';
import React from 'react';

import { Icons } from '../../assets/icons/DashboardIcons';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onDownload?: () => void;
  downloadButtonText?: string;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  onDownload,
  downloadButtonText = 'Download Report',
  className = '',
}) => {
  const getVisiblePages = () => {
    const pages = [];
    const showPages = 5;

    let startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + showPages - 1);

    if (endPage - startPage + 1 < showPages) {
      startPage = Math.max(1, endPage - showPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <div
      className={`mt-auto flex w-full items-center justify-between ${className}`}
    >
      {/* Download Button */}
      {onDownload && (
        <button
          onClick={onDownload}
          className="flex h-10 items-center gap-2 rounded-lg bg-[#4F4F4F] px-4 py-2 text-white transition-colors hover:bg-[#3F3F3F]"
        >
          {downloadButtonText}
          <Icons.DownloadCircle className="h-5 w-5" />
        </button>
      )}

      {/* Pagination Controls */}
      <div className="ml-auto flex items-center gap-2">
        {/* Previous Button */}
        <button
          onClick={handlePrevious}
          disabled={currentPage === 1}
          className={`flex items-center gap-1 rounded-lg px-3 py-2 transition-colors ${
            currentPage === 1
              ? 'cursor-not-allowed text-gray-400'
              : 'text-primary hover:bg-primary/10'
          }`}
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </button>

        {/* Page Numbers */}
        <div className="flex items-center gap-1">
          {getVisiblePages().map(page => (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={`flex h-8 min-w-8 items-center justify-center rounded-md transition-colors ${
                page === currentPage
                  ? 'bg-primary font-medium text-white'
                  : 'text-subText'
              }`}
            >
              {page}
            </button>
          ))}
        </div>

        {/* Next Button */}
        <button
          onClick={handleNext}
          disabled={currentPage === totalPages}
          className={`flex items-center gap-1 rounded-lg px-3 py-2 transition-colors ${
            currentPage === totalPages
              ? 'cursor-not-allowed text-gray-400'
              : 'text-primary hover:bg-primary/10'
          }`}
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export default Pagination;
