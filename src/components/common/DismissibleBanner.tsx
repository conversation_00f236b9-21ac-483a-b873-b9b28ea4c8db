'use client';

import { AnimatePresence, motion } from 'framer-motion';

interface DismissibleBannerProps {
  id: string;
  title: string;
  message: React.ReactNode;
  variant?: 'info' | 'warning' | 'success';
  hide?: boolean;
}

export function DismissibleBanner({
  title,
  message,
  variant = 'success',
  hide = false,
}: DismissibleBannerProps) {
  const variantStyles = {
    info: 'bg-blue-50',
    warning: 'bg-yellow-50',
    success: 'bg-[#1FC16B]/10',
  };

  if (hide) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{ height: 'auto', opacity: 1 }}
        exit={{ height: 0, opacity: 0 }}
        transition={{ duration: 0.3 }}
        className={`border-b px-5 py-3 ${variantStyles[variant]}`}
      >
        <div className="flex items-start justify-between">
          <div className="flex flex-col items-start gap-2">
            <div className="flex items-center gap-2">
              <svg
                width="21"
                height="20"
                viewBox="0 0 21 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M20.9297 10C20.9297 15.523 16.4527 20 10.9297 20C5.40669 20 0.929688 15.523 0.929688 10C0.929688 4.477 5.40669 0 10.9297 0C16.4527 0 20.9297 4.477 20.9297 10ZM9.92969 6C9.92969 6.26522 10.035 6.51957 10.2226 6.70711C10.4101 6.89464 10.6645 7 10.9297 7H10.9377C11.2029 7 11.4573 6.89464 11.6448 6.70711C11.8323 6.51957 11.9377 6.26522 11.9377 6C11.9377 5.73478 11.8323 5.48043 11.6448 5.29289C11.4573 5.10536 11.2029 5 10.9377 5H10.9297C10.6645 5 10.4101 5.10536 10.2226 5.29289C10.035 5.48043 9.92969 5.73478 9.92969 6ZM10.9297 15C11.1949 15 11.4493 14.8946 11.6368 14.7071C11.8243 14.5196 11.9297 14.2652 11.9297 14V9C11.9297 8.73478 11.8243 8.48043 11.6368 8.29289C11.4493 8.10536 11.1949 8 10.9297 8C10.6645 8 10.4101 8.10536 10.2226 8.29289C10.035 8.48043 9.92969 8.73478 9.92969 9V14C9.92969 14.2652 10.035 14.5196 10.2226 14.7071C10.4101 14.8946 10.6645 15 10.9297 15Z"
                  fill="#121212"
                />
              </svg>
              <div className="text-sm font-semibold text-blackTwo">{title}</div>
            </div>
            <div className="text-sm font-light">{message}</div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
