import classNames from 'classnames';
import React from 'react';

const AppContainer = ({
  children,
  className,
  isPadding = true,
}: {
  children: React.ReactNode;
  className?: string;
  isPadding?: boolean;
}) => {
  return (
    <div
      className={classNames(
        'max-w-[1640px]',
        isPadding ? 'p-4 md:p-6' : 'p-0',
        className
      )}
    >
      {children}
    </div>
  );
};

export default AppContainer;
