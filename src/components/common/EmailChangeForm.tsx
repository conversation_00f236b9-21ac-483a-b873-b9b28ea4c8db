import { Loader } from 'lucide-react';
import React, { useState } from 'react';

import { Input } from '@/components/ui';

interface EmailChangeFormProps {
  currentEmail: string;
  isLoading: boolean;
  error: string | null;
  onSubmit: (newEmail: string) => void;
  setError: (error: string | null) => void;
}

export const EmailChangeForm: React.FC<EmailChangeFormProps> = ({
  currentEmail,
  isLoading,
  onSubmit,
  setError,
}) => {
  const [newEmail, setNewEmail] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newEmail || newEmail === currentEmail) {
      setError('Please enter a valid new email address');
      return;
    }
    onSubmit(newEmail);
  };

  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="flex w-full max-w-md flex-col gap-4 rounded-xl bg-white p-5">
        <form onSubmit={handleSubmit} className="space-y-5">
          <h3 className="text-center text-sm font-normal text-subText sm:text-base lg:text-lg">
            Change email
          </h3>
          <div>
            <Input
              type="email"
              value={newEmail}
              onChange={e => setNewEmail(e.target.value)}
              placeholder="Email address"
              className="h-12 w-full rounded-md border border-[#DFEAF2] px-3 py-2 "
              required
              disabled={isLoading}
            />
          </div>

          <button
            type="submit"
            disabled={isLoading || !newEmail || newEmail === currentEmail}
            className="h-12 w-full rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2 hover:text-white">
                <Loader className="h-4 w-4 animate-spin" /> Saving...
              </div>
            ) : (
              'Save Changes'
            )}
          </button>
        </form>
      </div>
    </div>
  );
};
