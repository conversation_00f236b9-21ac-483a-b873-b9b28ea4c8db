import { yupResolver } from '@hookform/resolvers/yup';
import { <PERSON>, EyeOff, Loader } from 'lucide-react';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import { Input } from '@/components/ui';

interface PasswordChangeFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface PasswordChangeFormProps {
  isLoading: boolean;
  error: string | null;
  success: string | null;
  onSubmit: (data: PasswordChangeFormData) => void;
  setError: (error: string | null) => void;
  onForgotPassword?: () => void;
  showCurrentPassword?: boolean;
}

const createChangePasswordSchema = () => {
  const baseSchema = {
    newPassword: yup
      .string()
      .required('New password is required')
      .min(8, 'Password must be at least 8 characters')
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
      ),
    confirmPassword: yup
      .string()
      .required('Please confirm your password')
      .oneOf([yup.ref('newPassword')], 'Passwords must match'),

    currentPassword: yup.string().required('Current password is required'),
  };

  return yup.object().shape(baseSchema);
};

export const PasswordChangeForm: React.FC<PasswordChangeFormProps> = ({
  isLoading,
  onSubmit,
  onForgotPassword,
}) => {
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<PasswordChangeFormData>({
    resolver: yupResolver(createChangePasswordSchema()),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="flex w-full max-w-md flex-col items-center gap-4 rounded-xl bg-white p-5">
        <h1 className="text-lg font-semibold text-blackOne">Change Password</h1>
        <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-6">
          <div>
            <div className="relative">
              <Controller
                name="currentPassword"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type={showPasswords.current ? 'text' : 'password'}
                    placeholder="Enter current password"
                    className={`h-12 w-full rounded-md border px-3 py-2 pr-10 font-spartan ${
                      errors.currentPassword
                        ? 'border-red-500'
                        : 'border-[#DFEAF2]'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('current')}
                className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
              >
                {showPasswords.current ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.currentPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.currentPassword.message}
              </p>
            )}
          </div>

          <div>
            <div className="relative">
              <Controller
                name="newPassword"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type={showPasswords.new ? 'text' : 'password'}
                    placeholder="Enter new password"
                    className={`h-12 w-full rounded-md border px-3 py-2 pr-10 font-spartan ${
                      errors.newPassword ? 'border-red-500' : 'border-[#DFEAF2]'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('new')}
                className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
              >
                {showPasswords.new ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.newPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.newPassword.message}
              </p>
            )}
          </div>

          <div>
            <div className="relative">
              <Controller
                name="confirmPassword"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type={showPasswords.confirm ? 'text' : 'password'}
                    placeholder="Confirm new password"
                    className={`h-12 w-full rounded-md border px-3 py-2 pr-10 font-spartan ${
                      errors.confirmPassword
                        ? 'border-red-500'
                        : 'border-[#DFEAF2]'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('confirm')}
                className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
              >
                {showPasswords.confirm ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>

          {onForgotPassword && (
            <div className="space-y-1 text-sm sm:text-base">
              <button
                type="button"
                onClick={onForgotPassword}
                className="font-spartan text-subText underline"
              >
                Forgot or never set up your password?
              </button>
              <br />
              <span className="cursor-pointer font-spartan text-primary underline">
                Request a new password here.
              </span>
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="flex h-12 w-full items-center justify-center rounded-lg border border-primary bg-transparent px-4 py-2.5 font-medium hover:bg-primary/90 hover:text-white disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <div className="flex items-center justify-center text-white">
                <Loader className="h-4 w-4 animate-spin" />
              </div>
            ) : (
              'Save Changes'
            )}
          </button>
        </form>
      </div>
    </div>
  );
};
