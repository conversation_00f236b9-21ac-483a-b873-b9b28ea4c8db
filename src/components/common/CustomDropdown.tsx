import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import { Icons } from '../../assets/icons/DashboardIcons';

interface DropdownOption {
  id: string;
  label: string;
  value: string;
}

interface CustomDropdownProps {
  options: DropdownOption[];
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  placeholder?: string;
  buttonText?: string;
  icon?: React.ReactNode;
  className?: string;
  isMultiSelect?: boolean;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  selectedValues,
  onSelectionChange,
  buttonText = 'Filter',
  icon = <Icons.DownloadCloud className="h-4 w-4" />,
  className = '',
  isMultiSelect = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleOptionClick = (optionValue: string) => {
    if (isMultiSelect) {
      const newSelectedValues = selectedValues.includes(optionValue)
        ? selectedValues.filter(v => v !== optionValue)
        : [...selectedValues, optionValue];
      onSelectionChange(newSelectedValues);
    } else {
      onSelectionChange([optionValue]);
      setIsOpen(false);
    }
  };

  const getButtonText = () => {
    if (selectedValues.length === 0) {
      return buttonText;
    }

    if (selectedValues.length === 1) {
      const option = options.find(opt => opt.value === selectedValues[0]);
      return option ? option.label : buttonText;
    }

    return `${selectedValues.length} selected`;
  };

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* Dropdown Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="group flex items-center gap-2 rounded-lg border border-primary bg-[#FDF7F6] px-4 py-2 text-primary transition-colors hover:bg-primary hover:text-white"
      >
        <span className="font-medium">{getButtonText()}</span>
        {icon}
        <ChevronDown
          className={`hidden h-4 w-4 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-full z-50 mt-2 w-48 rounded-xl border border-gray-200 bg-white shadow-lg"
          >
            <div className="py-2">
              {options.length > 0 ? (
                options.map(option => (
                  <button
                    key={option.id}
                    onClick={() => handleOptionClick(option.value)}
                    className={`flex w-full items-center justify-between px-4 py-2 text-left text-sm transition-colors hover:bg-gray-50 ${
                      selectedValues.includes(option.value)
                        ? 'bg-lightOrangeTwo font-medium text-primary'
                        : 'text-subText'
                    }`}
                  >
                    <span>{option.label}</span>
                    {selectedValues.includes(option.value) && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="h-2 w-2 rounded-full bg-primary"
                      />
                    )}
                  </button>
                ))
              ) : (
                <div className="p-2">
                  <span className="text-subText">No options available</span>
                </div>
              )}
            </div>

            {/* Clear All Button for Multi-Select */}
            {isMultiSelect && selectedValues.length > 0 && (
              <div className="border-t border-gray-200 p-2">
                <button
                  onClick={() => onSelectionChange([])}
                  className="w-full rounded-md px-3 py-2 text-sm text-red-600 transition-colors hover:bg-red-50"
                >
                  Clear All
                </button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CustomDropdown;
