import { JSXElementConstructor, ReactNode, Suspense } from 'react';

import { Spinner } from '@/components/common/Loader';

export const MainLoaderSkeleton = () => {
  return (
    <div
      className={`absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]`}
    >
      <Spinner className="h-[50px] w-[50px] border-[6px]" />
    </div>
  );
};

export const withSuspense =
  /* eslint-disable react/display-name */

  (Component: JSXElementConstructor<any>) =>
    /* eslint-disable react/display-name */
    ({ children }: { children?: ReactNode }) => (
      <Suspense fallback={<MainLoaderSkeleton />}>
        <Component>{children && children}</Component>
      </Suspense>
    );
