const AgentSkeleton = () => (
  <div className="flex w-[261px] min-w-[261px] snap-start flex-col rounded border bg-white shadow-sm">
    {/* Image skeleton */}
    <div className="bg-peachTwo">
      <div className="h-56 w-full animate-pulse bg-gray-200"></div>
    </div>
    <div className="flex flex-1 flex-col gap-2 p-4">
      {/* Agent name badge skeleton */}
      <div className="h-6 w-20 animate-pulse rounded bg-gray-200"></div>
      {/* Description skeleton */}
      <div className="mt-1 h-6 w-full animate-pulse rounded bg-gray-200"></div>
      {/* Role description skeleton - multiple lines */}
      <div className="space-y-2">
        <div className="h-4 w-full animate-pulse rounded bg-gray-200"></div>
        <div className="h-4 w-3/4 animate-pulse rounded bg-gray-200"></div>
      </div>
    </div>
  </div>
);

export default AgentSkeleton;
