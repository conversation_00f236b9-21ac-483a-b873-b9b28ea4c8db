import clsx from 'clsx';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';
import React from 'react';

export interface DropdownOption {
  id: string;
  name: string;
  icon: string;
}

interface AgentsDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  currentItem?: DropdownOption;
  options: DropdownOption[];
  onItemSelect: (option: DropdownOption) => void;
  placeholder: string;
  noOptionsMessage: string;
  onImageError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  getOptionImageError?: (
    option: DropdownOption
  ) => (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  className?: string;
}

const AgentsDropdown: React.FC<AgentsDropdownProps> = ({
  isOpen,
  onToggle,
  currentItem,
  options,
  onItemSelect,
  placeholder,
  noOptionsMessage,
  onImageError,
  getOptionImageError,
  className,
}) => {
  return (
    <div className="relative">
      <button
        onClick={onToggle}
        type="button"
        className={clsx(
          'flex h-[48px] items-center gap-3 rounded-[10px] border border-gray-200 bg-white p-2 focus:border-transparent focus:outline-none focus:ring-0 md:w-fit',
          className
        )}
      >
        <div className="flex w-full items-center gap-3">
          {currentItem && currentItem.icon ? (
            <img
              src={currentItem?.icon || ''}
              alt={currentItem?.name || placeholder}
              className="h-6 w-6 rounded-lg bg-peachTwo object-cover sm:h-8 sm:w-8"
              onError={onImageError}
            />
          ) : (
            <div className="h-8 w-8 rounded-lg bg-peachTwo" />
          )}
          <span className="text-sm font-normal text-blackOne">
            {currentItem?.name || placeholder}
          </span>
        </div>
        {isOpen ? (
          <ChevronUp className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-500" />
        )}
      </button>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute left-0 top-full z-50 mt-1 w-full min-w-64 rounded-xl border border-gray-200 bg-white shadow-[0px_8px_16px_-2px_#1B212C1F]"
        >
          <div className="py-1">
            {options.length &&
            options.filter(option => option.id !== currentItem?.id).length >
              0 ? (
              options
                .filter(option => option.id !== currentItem?.id)
                .map(option => (
                  <button
                    key={option.id}
                    onClick={() => onItemSelect(option)}
                    className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm font-medium transition-colors hover:bg-gray-50 sm:text-base"
                  >
                    <img
                      src={option.icon}
                      alt={option.name}
                      className="h-6 w-6 rounded-lg bg-peachTwo object-cover sm:h-8 sm:w-8"
                      onError={getOptionImageError?.(option)}
                    />
                    {option.name}
                  </button>
                ))
            ) : (
              <div className="px-4 py-2 text-sm text-gray-500">
                {noOptionsMessage}
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default AgentsDropdown;
