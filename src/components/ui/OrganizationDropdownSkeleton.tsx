import { ChevronDown } from 'lucide-react';
import React from 'react';

const OrganizationDropdownSkeleton: React.FC = () => {
  return (
    <div className="relative">
      <div
        className="flex animate-pulse items-center gap-3 rounded-lg border border-gray-300 bg-white px-4 py-2"
        style={{ width: '203px' }}
      >
        {/* Icon skeleton */}
        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-300">
          <div className="h-4 w-4 rounded bg-gray-400"></div>
        </div>

        {/* Text skeleton */}
        <div className="flex-1">
          <div className="h-4 w-24 rounded bg-gray-300"></div>
        </div>

        {/* Chevron */}
        <ChevronDown className="h-4 w-4 flex-shrink-0 text-gray-400" />
      </div>
    </div>
  );
};

export default OrganizationDropdownSkeleton;
