import { FileText } from 'lucide-react';
import { useEffect, useState } from 'react';

interface FilePreviewProps {
  url?: string;
  fileName: string;
  className?: string;
}

const FilePreview = ({ url, fileName, className = '' }: FilePreviewProps) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (!url) {
      setError(true);
      setIsLoading(false);
      return;
    }

    const generatePreview = async () => {
      try {
        setIsLoading(true);
        setError(false);

        // Check if it's a PDF (common case for document previews)
        if (url.toLowerCase().endsWith('.pdf')) {
          // For PDFs, use the first page as preview
          setPreviewUrl(`${url}#page=1`);
        }
        // Check if it's an image
        else if (url.match(/\.(jpeg|jpg|gif|png|webp)$/i)) {
          setPreviewUrl(url);
        }
        // For other file types, show icon
        else {
          setError(true);
        }
      } catch (err) {
        console.error('Error generating preview:', err);
        setError(true);
      } finally {
        setIsLoading(false);
      }
    };

    generatePreview();
  }, [url]);

  if (isLoading) {
    return (
      <div
        className={`flex animate-pulse items-center justify-center bg-gray-100 ${className}`}
      >
        <div className="h-4 w-4 rounded-full bg-gray-300"></div>
      </div>
    );
  }

  if (error || !previewUrl) {
    return (
      <div
        className={`flex items-center justify-center border bg-gray-50 ${className}`}
      >
        <FileText className="h-8 w-8 text-gray-400" strokeWidth={1} />
      </div>
    );
  }

  // For PDFs (using iframe)
  if (url?.toLowerCase().endsWith('.pdf')) {
    return (
      <iframe
        src={previewUrl}
        className={`border-0 ${className}`}
        title={`Preview of ${fileName}`}
        loading="lazy"
      />
    );
  }

  // For images
  return (
    <img
      src={previewUrl}
      alt={`Preview of ${fileName}`}
      className={`object-cover ${className}`}
      loading="lazy"
      onError={() => setError(true)}
    />
  );
};

export default FilePreview;
