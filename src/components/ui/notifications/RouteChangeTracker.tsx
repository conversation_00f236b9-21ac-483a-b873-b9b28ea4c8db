import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import { useNotifications } from '@/context/NotificationContext';

export const RouteChangeTracker = () => {
  const location = useLocation();
  const { clearNotifications } = useNotifications();

  useEffect(() => {
    // Clear notifications when route changes
    clearNotifications();
  }, [location]);

  return null; // This component doesn't render anything
};
