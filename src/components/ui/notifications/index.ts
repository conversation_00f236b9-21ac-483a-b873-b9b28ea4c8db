/**
 * Notification Toast System - Main Export File
 *
 * This file provides a centralized export for all notification-related components,
 * hooks, and types. Import from this file to access the complete notification system.
 *
 *
 * Using notifications in components:
 * ```tsx
 * const MyComponent = () => {
 *   const { notify, notifyWithImage } = useNotifications();
 *
 *   const handleSuccess = () => {
 *     notify("I have successfully connected to Salesforce");
 *   };
 *
 *   const handleWithLogo = () => {
 *     notifyWithImage(
 *       "SetIQ suite claimed",
 *       "/path/to/setiq-logo.png",
 *       "h-6 w-6 rounded"
 *     );
 *   };
 * };
 * ```
 */

// Core Components
export { default as NotificationContainer } from '../NotificationContainer';
export { default as NotificationToast } from '../NotificationToast';

// Context and Providers
export {
  NotificationProvider,
  useNotificationContext,
} from '../../../context/NotificationContext';

// Hooks
export { useNotifications } from '../../../hooks/useNotifications';

// Types
export type {
  AddNotificationOptions,
  ExtendedNotificationData,
  NotificationAgent,
  NotificationAnimationState,
  NotificationContainerProps,
  NotificationContextValue,
  NotificationData,
  NotificationPosition,
  NotificationProviderProps,
  NotificationSeverity,
  NotificationToastProps,
} from '../../../types/notifications';

// Re-export the main hook as default for convenience
export { useNotifications as default } from '../../../hooks/useNotifications';
