import React from 'react';

const OrganizationCardSkeleton: React.FC = () => {
  return (
    <div className="animate-pulse text-center">
      <div className="mb-6">
        <div className="mx-auto mb-6 h-48 w-80 rounded-2xl bg-gray-300 shadow-lg"></div>
      </div>

      <div className="mx-auto mb-6 h-6 w-32 rounded bg-gray-300"></div>

      <div className="mx-auto h-10 w-40 rounded-lg bg-gray-300"></div>
    </div>
  );
};

const OrganizationSkeletonLoader: React.FC<{ count?: number }> = ({
  count = 2,
}) => {
  return (
    <div className="flex flex-wrap justify-center gap-8">
      {Array.from({ length: count }, (_, index) => (
        <OrganizationCardSkeleton key={index} />
      ))}
    </div>
  );
};

export { OrganizationCardSkeleton, OrganizationSkeletonLoader };
export default OrganizationSkeletonLoader;
