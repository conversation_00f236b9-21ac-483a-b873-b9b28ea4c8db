import {
  endOfMonth,
  endOfWeek,
  endOfYear,
  startOfMonth,
  startOfWeek,
  startOfYear,
} from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useState } from 'react';

import { Calendar } from './Calendar';

interface DateRange {
  from?: Date;
  to?: Date;
}

interface DateRangePickerProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (range: DateRange) => void;
  initialRange?: DateRange;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  isOpen,
  onClose,
  onApply,
  initialRange,
}) => {
  const [selectedRange, setSelectedRange] = useState<DateRange>(
    initialRange || { from: undefined, to: undefined }
  );
  const [quickSelectOption, setQuickSelectOption] = useState<string>('set_up');

  const handleQuickSelect = (option: string) => {
    setQuickSelectOption(option);
    const now = new Date();
    let range: DateRange = { from: undefined, to: undefined };

    switch (option) {
      case 'today':
        range = { from: now, to: now };
        break;
      case 'this_week': {
        const weekStart = startOfWeek(now, { weekStartsOn: 0 }); // Sunday start
        const weekEnd = endOfWeek(now, { weekStartsOn: 0 });
        range = { from: weekStart, to: weekEnd };
        break;
      }
      case 'this_month': {
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);
        range = { from: monthStart, to: monthEnd };
        break;
      }
      case 'this_year': {
        const yearStart = startOfYear(now);
        const yearEnd = endOfYear(now);
        range = { from: yearStart, to: yearEnd };
        break;
      }
      case 'set_up':
        // Keep current selection
        break;
    }

    if (option !== 'set_up') {
      setSelectedRange(range);
    }
  };

  const handleApply = () => {
    onApply(selectedRange);
    onClose();
  };

  const handleCancel = () => {
    setSelectedRange(initialRange || { from: undefined, to: undefined });
    onClose();
  };

  const quickSelectOptions = [
    { id: 'today', label: 'Today' },
    { id: 'this_week', label: 'This week' },
    { id: 'this_month', label: 'This month' },
    { id: 'this_year', label: 'This year' },
    { id: 'set_up', label: 'Set up' },
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
        onClick={handleCancel}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="mx-4 w-full max-w-4xl rounded-2xl bg-white p-6 shadow-2xl"
          onClick={e => e.stopPropagation()}
        >
          <div className="flex gap-6">
            {/* Quick Select Options */}
            <div className="flex-shrink-0">
              <div className="space-y-2">
                {quickSelectOptions.map(option => (
                  <label
                    key={option.id}
                    className="flex cursor-pointer items-center gap-3"
                  >
                    <input
                      type="radio"
                      name="dateRange"
                      checked={quickSelectOption === option.id}
                      onChange={() => handleQuickSelect(option.id)}
                      className="h-4 w-4 text-primary"
                    />
                    <span className="text-sm text-gray-700">
                      {option.label}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Calendar */}
            <div className="flex-1">
              <Calendar
                mode="range"
                selected={{
                  from: selectedRange.from,
                  to: selectedRange.to,
                }}
                onSelect={range => {
                  if (range) {
                    setSelectedRange({
                      from: range.from,
                      to: range.to,
                    });
                    setQuickSelectOption('set_up');
                  }
                }}
                numberOfMonths={2}
                showOutsideDays={false}
                className="w-full rounded-md border"
              />
            </div>
          </div>

          {/* Date Display and Actions */}
          <div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-4">
            <div className="flex gap-4">
              {selectedRange.from && (
                <div className="rounded-full bg-gray-100 px-4 py-2 text-sm">
                  {selectedRange.from.toLocaleDateString('en-US', {
                    month: '2-digit',
                    day: '2-digit',
                    year: 'numeric',
                  })}
                </div>
              )}
              {selectedRange.to && (
                <div className="rounded-full bg-gray-100 px-4 py-2 text-sm">
                  {selectedRange.to.toLocaleDateString('en-US', {
                    month: '2-digit',
                    day: '2-digit',
                    year: 'numeric',
                  })}
                </div>
              )}
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleCancel}
                className="px-6 py-2 text-gray-600 transition-colors hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleApply}
                className="rounded-lg bg-black px-6 py-2 text-white transition-colors hover:bg-gray-800"
              >
                Apply
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default DateRangePicker;
