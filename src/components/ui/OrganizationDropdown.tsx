import React from 'react';

import { organizationPlaceholderDrLogo } from '../../assets/images';
import { useAuth } from '../../context/AuthContext';
import OrganizationDropdownSkeleton from './OrganizationDropdownSkeleton';

const OrganizationDropdown: React.FC = () => {
  const { user, isLoading } = useAuth();

  // Show skeleton while loading
  if (isLoading) {
    return <OrganizationDropdownSkeleton />;
  }

  // Don't show anything if no user or tenant
  if (!user?.tenant) {
    return null;
  }

  // Get current tenant display name from user data
  const tenantName =
    user.tenant.claimedAgentSuites?.[0]?.suite?.agentSuiteName ||
    'My Workspace';

  return (
    <div
      className="flex items-center gap-3 rounded-lg border border-primary bg-white p-2 text-primary"
      style={{ width: '203px' }}
    >
      <div className="h-7 w-7 overflow-hidden rounded-lg">
        <img
          src={organizationPlaceholderDrLogo}
          alt="Organization Icon"
          className="h-full w-full object-cover"
        />
      </div>
      <span className="flex-1 truncate text-left font-medium text-blackOne">
        {tenantName}
      </span>
    </div>
  );
};

export default OrganizationDropdown;
