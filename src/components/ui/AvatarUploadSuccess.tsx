'use client';

import { Check } from 'lucide-react';
import { useEffect, useState } from 'react';

import { profilePlaceholder } from '@/assets/images';

interface AvatarUploadSuccessProps {
  src?: string;
  alt?: string;
  fallback?: string;
  isSuccess: boolean;
  isUploading?: boolean;
  uploadProgress?: number;
  onAnimationComplete?: () => void;
  className?: string;
}

export function AvatarUploadSuccess({
  src,
  alt,
  fallback,
  isSuccess,
  isUploading,
  uploadProgress = 0,
  onAnimationComplete,
  className,
}: AvatarUploadSuccessProps) {
  const [showCheckmark, setShowCheckmark] = useState(false);
  const [showRing, setShowRing] = useState(false);

  useEffect(() => {
    if (isSuccess) {
      setShowCheckmark(false);
      setShowRing(false);

      const timer1 = setTimeout(() => setShowRing(true), 100);
      const timer2 = setTimeout(() => setShowCheckmark(true), 300);
      const timer3 = setTimeout(() => {
        setShowCheckmark(false);
        setShowRing(false);
        onAnimationComplete?.();
      }, 2000);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearTimeout(timer3);
      };
    }
  }, [isSuccess, onAnimationComplete]);

  return (
    <div className={`relative ${className || ''}`}>
      <div
        className={`flex h-20 w-20 items-center justify-center overflow-hidden rounded-full border-2 transition-all duration-500 ease-out lg:h-[150px] lg:w-[150px] ${
          isSuccess
            ? 'scale-105 border-primary/20 shadow-lg shadow-primary/25'
            : 'border-0'
        }`}
      >
        <img
          src={src || profilePlaceholder}
          alt={alt}
          className={`h-full w-full object-cover transition-all duration-300 ${
            isUploading ? 'blur-sm' : ''
          }`}
        />

        {/* Loading Progress Overlay */}
        {isUploading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center rounded-full bg-black bg-opacity-60">
            {/* Circular Progress */}
            <div className="relative">
              <svg
                className="h-12 w-12 -rotate-90 transform"
                viewBox="0 0 36 36"
              >
                <path
                  className="stroke-gray-300"
                  strokeWidth="3"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  className="stroke-primary"
                  strokeWidth="3"
                  strokeDasharray={`${uploadProgress}, 100`}
                  strokeLinecap="round"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-[10px] font-medium text-white">
                  {Math.round(uploadProgress)}%
                </span>
              </div>
            </div>
          </div>
        )}

        {fallback && !src && (
          <div className="bg-muted text-muted-foreground flex h-full w-full items-center justify-center text-2xl font-semibold">
            {fallback}
          </div>
        )}
      </div>

      {/* Success Checkmark */}
      {isSuccess && (
        <div
          className={`absolute inset-0 flex items-center justify-center transition-all duration-500 ease-out ${
            showCheckmark ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
          }`}
        >
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary shadow-lg">
            <Check className="h-8 w-8 text-white" />
          </div>
        </div>
      )}

      {/* Success Ring Animation */}
      {isSuccess && (
        <div
          className={`pointer-events-none absolute inset-0 rounded-full border-2 border-primary/60 transition-all duration-1000 ease-out ${
            showRing ? 'scale-125 opacity-0' : 'scale-100 opacity-100'
          }`}
        />
      )}
    </div>
  );
}
