import { AnimatePresence } from 'framer-motion';
import React, { useCallback, useEffect, useRef } from 'react';

import { cn } from '@/lib/twMerge/cn';
import { NotificationContainerProps } from '@/types/notifications';

import NotificationToast from './NotificationToast';

/**
 * Container component for managing multiple notification toasts
 */
const NotificationContainer: React.FC<NotificationContainerProps> = ({
  notifications,
  onClose,
  className,
  maxNotifications = 5,
  size = 'large',
}) => {
  // Ref for the container element to enable scrolling
  const containerRef = useRef<HTMLDivElement>(null);

  // Track previous notification count to detect new additions
  const prevNotificationCountRef = useRef<number>(0);

  // Filter out notifications that are being removed and limit display count
  const visibleNotifications = notifications
    .filter(notification => !notification.isRemoving)
    .slice(0, maxNotifications);

  /**
   * Check if the top of the container is visible in the viewport
   * Returns true if auto-scroll should be triggered
   */
  const shouldAutoScroll = useCallback((): boolean => {
    if (!containerRef.current) return false;

    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();
    const viewportHeight = window.innerHeight;

    // Auto-scroll if:
    // 1. The top of the container is above the viewport (scrolled past)
    // 2. The container is significantly below the fold (user scrolled down)
    // We use a buffer to ensure smooth UX - only scroll if clearly out of view
    const topBuffer = 50; // pixels above viewport
    const bottomBuffer = viewportHeight * 0.8; // 80% down the viewport

    const isAboveViewport = containerRect.top < -topBuffer;
    const isBelowFold = containerRect.top > bottomBuffer;

    return isAboveViewport || isBelowFold;
  }, []);

  /**
   * Smoothly scroll the container to the top to show the newest notification
   */
  const scrollToTop = useCallback(() => {
    if (!containerRef.current) return;

    // Use smooth scrolling with a slight delay to allow Framer Motion animations to start
    // This ensures the entrance animation is visible and not interrupted by scrolling
    setTimeout(() => {
      if (!containerRef.current) return;

      try {
        // First try scrollIntoView for better browser compatibility
        containerRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest',
        });
      } catch (error) {
        // Fallback for older browsers - scroll to top of page
        console.warn(
          '[NotificationContainer] scrollIntoView failed, using fallback:',
          error
        );
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
      }
    }, 150); // 150ms delay allows NotificationToast entrance animation (300ms) to start and be visible
  }, []);

  // Effect to handle auto-scroll when new notifications are added
  useEffect(() => {
    const currentCount = visibleNotifications.length;
    const prevCount = prevNotificationCountRef.current;

    // Only trigger auto-scroll if:
    // 1. We have notifications
    // 2. The count increased (new notification added)
    // 3. The container would not be visible to the user
    if (currentCount > 0 && currentCount > prevCount && shouldAutoScroll()) {
      scrollToTop();
    }

    // Update the previous count
    prevNotificationCountRef.current = currentCount;
  }, [visibleNotifications.length, shouldAutoScroll, scrollToTop]);

  // Don't render container if no notifications
  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        // Base container styles
        'w-full space-y-3',
        className
      )}
      role="region"
      aria-label="Notifications"
      aria-live="polite"
    >
      <AnimatePresence mode="popLayout">
        {visibleNotifications.map(notification => (
          <NotificationToast
            key={notification.id}
            notification={notification}
            onClose={onClose}
            className="w-full"
            size={size}
          />
        ))}
      </AnimatePresence>

      {/* Screen reader announcement for notification count */}
      <div className="sr-only" aria-live="polite">
        {visibleNotifications.length === 1
          ? '1 notification'
          : `${visibleNotifications.length} notifications`}
      </div>
    </div>
  );
};

export default NotificationContainer;
