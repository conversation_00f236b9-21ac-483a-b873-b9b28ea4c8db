/*
import { Link } from 'react-router-dom';
import Logo from '../ui/Logo';

type FooterLink = {
  to: string;
  text: string;
};

type FooterSection = {
  title: string;
  links: FooterLink[];
};

const footerLinks: FooterSection[] = [
  {
    title: 'Features',
    links: [
      { to: '/statements', text: 'Statements' },
      { to: '/grants', text: '<PERSON><PERSON>' },
      { to: '/learn', text: 'Learn' },
      { to: '/pricing', text: 'Pricing' },
    ],
  },
  {
    title: 'Company',
    links: [
      { to: '/about', text: 'About us' },
      { to: '/careers', text: 'Careers' },
      { to: '/press', text: 'Press' },
      { to: '/event', text: 'Event' },
    ],
  },
  {
    title: 'Legal',
    links: [
      { to: '/terms', text: 'Terms' },
      { to: '/privacy', text: 'Privacy' },
      { to: '/cookies', text: 'Cookies' },
      { to: '/contact', text: 'Contact' },
    ],
  },
];

const FooterLinkItem = ({ to, text }: FooterLink) => (
  <li>
    <Link
      to={to}
      className="flex gap-2 items-center font-medium transition-colors hover:text-gray-400"
    >
      {text}
      {text === 'Pricing' && (
        <span className="px-2 py-1 text-xs font-medium text-black rounded-full bg-whiteOff">
          New
        </span>
      )}
    </Link>
  </li>
);

const FooterSection = ({ title, links }: FooterSection) => (
  <div className="text-center md:text-left">
    <h3 className="mb-4 text-lg font-semibold text-gray-400">{title}</h3>
    <ul className="flex flex-col items-center space-y-2 md:items-start">
      {links.map(link => (
        <FooterLinkItem key={link.to} {...link} />
      ))}
    </ul>
  </div>
);
*/

import { Link } from 'react-router-dom';

export const Footer = () => {
  return (
    <footer className="text-white">
      <div className="mx-auto flex max-w-screen-3xl justify-center bg-darkBlueOne">
        {/* <div className="grid grid-cols-1 gap-8 md:grid-cols-5">
          {/* Logo and Description - Centered on mobile *}
          <div className="col-span-1 text-center md:col-span-2 md:text-left">
            <div className="flex justify-center md:block">
              <Logo variant="light" />
            </div>
            <p className="py-4 mx-auto max-w-md text-lg font-medium md:mx-0">
              The AI Agent Marketplace
              <br />
              and Transformation Layer
            </p>
          </div>

          {/* Footer Links - Centered on mobile *}
          {footerLinks.map(section => (
            <FooterSection key={section.title} {...section} />
          ))}
        </div> */}

        {/* Bottom text - Centered on mobile */}
        <div className="app-container flex flex-col items-center justify-center px-4 py-8 text-grayTwentyFive sm:px-0 md:flex-row md:gap-10">
          <p className="text-center text-sm">© 2025 by Agentous Inc.</p>
          <p className="mt-4 text-center text-sm md:mt-0">
            All rights are reserved. All trademarks, service marks, and logos
            used on this site are the property of Agentous Inc.
          </p>
          <Link
            to="/terms"
            className="mt-4 text-center text-sm hover:underline md:mt-0"
          >
            Terms of Use
          </Link>
        </div>
      </div>
    </footer>
  );
};
