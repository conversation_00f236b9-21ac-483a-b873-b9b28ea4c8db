import { Outlet, useLocation } from 'react-router-dom';

import { ErrorBoundary } from '../ui/ErrorBoundary';
import { Footer } from './Footer';
import { Navbar } from './Navbar';

const NavigationShell = () => {
  const location = useLocation();
  const currentPath = location.pathname;

  return (
    <ErrorBoundary>
      <div className="flex min-h-screen flex-col">
        <Navbar />
        <main className="flex-1">
          <Outlet />
        </main>
        {currentPath !== '/agents' && <Footer />}
      </div>
    </ErrorBoundary>
  );
};

export default NavigationShell;
