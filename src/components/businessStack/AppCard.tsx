import { EllipsisVertical } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import { connectLine, disconnectLine } from '../../assets/icons';
import { AppCardProps } from '../../types/businessStack';
import PreConnectionModal from './PreConnectionModal';

export const AppCard: React.FC<AppCardProps> = ({
  app,
  onConnect,
  connectionFlow,
  onOpenTwilioModal,
}) => {
  const [showOptionsMenu, setShowOptionsMenu] = useState(false);
  const [showPreConnectionModal, setShowPreConnectionModal] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowOptionsMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleConnect = () => {
    if (!app.isConnected) {
      if (app.name === 'Twilio' && onOpenTwilioModal) {
        onOpenTwilioModal();
      } else {
        // Show pre-connection modal before starting connection flow
        setShowPreConnectionModal(true);
      }
    }
  };

  const handleConfirmConnection = () => {
    setShowPreConnectionModal(false);

    if (connectionFlow) {
      // Step 1: Display connection message (as referenced in the comment)
      connectionFlow.startConnection(app);
    } else {
      // Fallback to default connection handler
      onConnect(app.name);
    }
  };

  const handleCancelConnection = () => {
    setShowPreConnectionModal(false);
  };

  const handleMenuOptionClick = (
    action: 'connect' | 'disconnect' | 'reconnect'
  ) => {
    setShowOptionsMenu(false);

    switch (action) {
      case 'connect':
        if (app.name === 'Twilio' && onOpenTwilioModal) {
          onOpenTwilioModal();
        } else {
          // Show pre-connection modal before starting connection flow
          setShowPreConnectionModal(true);
        }
        break;
      case 'disconnect':
        if (connectionFlow) {
          connectionFlow.handleDisconnect(app);
        } else {
          console.log('Disconnect functionality called');
        }
        break;
      case 'reconnect':
        if (app.name === 'Twilio' && onOpenTwilioModal) {
          onOpenTwilioModal();
        } else if (connectionFlow) {
          connectionFlow.handleReconnect(app);
        } else {
          console.log('Reconnect functionality called');
        }
        break;
    }
  };

  return (
    <div className="group relative flex cursor-pointer flex-col items-center rounded-lg border py-4 transition-all hover:border-primary hover:shadow-lg">
      {/* Options Menu */}
      <div ref={dropdownRef} className="absolute right-1 top-2">
        <button
          onClick={() => setShowOptionsMenu(!showOptionsMenu)}
          className="flex h-7 w-7 items-center justify-center rounded-full text-gray-400 opacity-60 transition-all group-hover:opacity-100 hover:bg-gray-100 hover:text-gray-600"
        >
          <EllipsisVertical className="h-6 w-6" />
        </button>

        {showOptionsMenu && (
          <div className="absolute right-0 top-full z-10 mt-1 w-32 rounded-lg border border-gray-200 bg-white shadow-lg">
            <div className="py-1">
              {!app.isConnected && (
                <button
                  onClick={() => handleMenuOptionClick('connect')}
                  className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50"
                >
                  <img
                    src={connectLine}
                    alt="Connect"
                    className="h-6 w-6 object-cover"
                  />
                  <span>Connect</span>
                </button>
              )}
              {app.isConnected && (
                <button
                  onClick={() => handleMenuOptionClick('disconnect')}
                  className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50"
                >
                  <img
                    src={disconnectLine}
                    alt="Disconnect"
                    className="h-6 w-6 object-cover"
                  />
                  <span>Disconnect</span>
                </button>
              )}
              {app.isConnected && (
                <button
                  onClick={() => handleMenuOptionClick('reconnect')}
                  className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50"
                >
                  <img
                    src={connectLine}
                    alt="Reconnect"
                    className="h-6 w-6 object-cover"
                  />
                  <span>Reconnect</span>
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* App Name */}
      <h3
        className="mb-2 cursor-pointer text-center text-sm font-bold text-blackThree transition-colors group-hover:text-primary md:text-nowrap"
        onClick={handleConnect}
      >
        {app.name}
      </h3>

      {/* App Logo */}
      <div
        className="mb-2.5 h-28 w-28 cursor-pointer rounded-md bg-grayTwentySeven p-6 shadow-sm transition-all group-hover:bg-blueTwo group-hover:shadow-md"
        onClick={handleConnect}
      >
        <div className="h-15 w-15 flex-shrink-0">
          <img
            src={app.logo}
            alt={`${app.name} logo`}
            className="h-full w-full rounded-lg object-contain"
            onError={e => {
              // Fallback for broken images
              const target = e.target as HTMLImageElement;
              target.src =
                'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjQgMjRIMzJWMzJIMjRWMjRaIiBmaWxsPSIjRDFEOUUwIi8+CjxwYXRoIGQ9Ik0zMiAyNEg0MFYzMkgzMlYyNFoiIGZpbGw9IiNEMUQ5RTAiLz4KPHBhdGggZD0iTTI0IDMySDMyVjQwSDI0VjMyWiIgZmlsbD0iI0QxRDlFMCIvPgo8cGF0aCBkPSJNMzIgMzJINDBWNDBIMzJWMzJaIiBmaWxsPSIjRDFEOUUwIi8+Cjwvc3ZnPgo=';
            }}
          />
        </div>
      </div>

      {/* Connection Button */}
      <button
        onClick={handleConnect}
        disabled={app.isConnected}
        className={`flex h-10 w-32 items-center justify-center rounded-full border border-blackOne px-8 py-2 font-medium transition-colors group-hover:border-none ${
          app.isConnected
            ? 'cursor-not-allowed bg-blackOne text-white group-hover:cursor-not-allowed'
            : 'group-hover:bg-primary group-hover:text-white'
        }`}
      >
        <span>{app.isConnected ? 'Connected' : 'Connect'}</span>
      </button>

      {/* Pre-Connection Modal */}
      <PreConnectionModal
        isOpen={showPreConnectionModal}
        onClose={handleCancelConnection}
        onConfirm={handleConfirmConnection}
        app={app}
      />
    </div>
  );
};
