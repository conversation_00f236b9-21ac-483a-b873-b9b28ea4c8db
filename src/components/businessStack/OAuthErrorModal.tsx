import { AlertTriangle } from 'lucide-react';
import React from 'react';

import AnimatedModal from '../common/AnimatedModal';

interface OAuthErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  appName: string;
}

const OAuthErrorModal: React.FC<OAuthErrorModalProps> = ({
  isOpen,
  onClose,
  appName,
}) => {
  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={onClose}
      showCloseButton={false}
      // title="Connection Failed"
      maxWidth="md"
    >
      <div className="space-y-6 p-8 text-center">
        {/* Error Icon */}
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-orange-100">
          <AlertTriangle className="h-8 w-8 text-orange-600" />
        </div>

        {/* Error Message */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-blackOne">
            Connection Failed
          </h3>
          <p className="text-sm leading-relaxed text-gray-600">
            You're signed in, but this app isn't authorized to access your{' '}
            {appName} account. <br />
            Please contact your company's {appName} admin.
          </p>
        </div>

        {/* Action Button */}
        <div className="pt-2">
          <button
            onClick={onClose}
            className="rounded-md bg-primary px-6 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/20"
          >
            Got it
          </button>
        </div>
      </div>
    </AnimatedModal>
  );
};

export default OAuthErrorModal;
