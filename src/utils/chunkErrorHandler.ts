import React from 'react';

/**
 * Utility to handle chunk loading errors and retry failed dynamic imports
 * This is specifically designed to handle the case where a user returns to a tab
 * after a new deployment has occurred, causing chunk hashes to change
 */

interface RetryOptions {
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Wraps a dynamic import with retry logic for chunk loading failures
 */
export const retryChunkLoad = async <T>(
  importFn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  const { maxRetries = 2, retryDelay = 1000 } = options;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await importFn();
    } catch (error) {
      const isChunkLoadError =
        error instanceof Error &&
        (error.message.includes(
          'Failed to fetch dynamically imported module'
        ) ||
          error.message.includes('Loading chunk') ||
          error.message.includes('ChunkLoadError') ||
          error.name === 'ChunkLoadError');

      // If this is the last attempt or not a chunk error, throw
      if (attempt === maxRetries || !isChunkLoadError) {
        throw error;
      }

      // For chunk errors, reload the page to get fresh chunk references
      if (attempt === 0) {
        // eslint-disable-next-line no-console
        console.warn('Chunk load failed, reloading page to get fresh assets');
        window.location.reload();
        return new Promise(() => {}); // Never resolve as page is reloading
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  throw new Error('Max retries exceeded');
};

/**
 * Enhanced lazy loading with chunk error handling
 */
export const lazyWithRetry = <
  T extends React.ComponentType<Record<string, unknown>>,
>(
  importFn: () => Promise<{ default: T }>,
  options?: RetryOptions
) => {
  return React.lazy(() => retryChunkLoad(importFn, options));
};

/**
 * Checks if an error is a chunk loading error
 */
export const isChunkError = (error: Error): boolean => {
  return (
    error.message.includes('Failed to fetch dynamically imported module') ||
    error.message.includes('Loading chunk') ||
    error.message.includes('ChunkLoadError') ||
    error.name === 'ChunkLoadError'
  );
};

/**
 * Global error handler for unhandled chunk errors
 * Call this in your main App component to catch any unhandled chunk errors
 */
export const setupChunkErrorHandler = () => {
  window.addEventListener('error', event => {
    const { error } = event;
    if (error && isChunkError(error)) {
      // eslint-disable-next-line no-console
      console.error('Unhandled chunk error detected, reloading page:', error);
      window.location.reload();
    }
  });

  window.addEventListener('unhandledrejection', event => {
    const { reason } = event;
    if (reason instanceof Error && isChunkError(reason)) {
      // eslint-disable-next-line no-console
      console.error(
        'Unhandled chunk promise rejection, reloading page:',
        reason
      );
      event.preventDefault(); // Prevent the default browser behavior
      window.location.reload();
    }
  });
};
