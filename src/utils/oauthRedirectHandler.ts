/**
 * OAuth Redirect Handler Utility
 *
 * This utility handles the OAuth redirect flow for various OAuth integrations.
 * It should be used on the redirect_uri page that OAuth providers redirect to after authentication.
 */

/**
 * Extracts OAuth parameters from the current URL
 * @returns Object containing state, code, error, and other OAuth parameters
 */
export const extractOAuthParams = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const hashParams = new URLSearchParams(window.location.hash.substring(1));

  return {
    state: urlParams.get('state') || hashParams.get('state'),
    code: urlParams.get('code') || hashParams.get('code'),
    error: urlParams.get('error') || hashParams.get('error'),
    errorDescription:
      urlParams.get('error_description') || hashParams.get('error_description'),
    adminConsent:
      urlParams.get('admin_consent') || hashParams.get('admin_consent'),
  };
};

/**
 * Checks if the current URL contains OAuth error parameters
 * @returns boolean indicating if OAuth error parameters are present
 */
export const hasOAuthError = (): boolean => {
  const params = extractOAuthParams();
  return !!params.error;
};

/**
 * Gets a formatted error message from OAuth error parameters
 * @returns string containing the error message or null if no error
 */
export const getOAuthErrorMessage = (): string | null => {
  const params = extractOAuthParams();
  if (!params.error) return null;

  return params.errorDescription || params.error || 'Authentication failed';
};

/**
 * Converts app key to display name with proper capitalization
 * @param appKey - The app key from URL parameters (e.g., 'salesforce', 'microsoft-outlook')
 * @returns Formatted app name for display (e.g., 'Salesforce', 'Microsoft Outlook')
 */
export const formatAppNameFromKey = (appKey: string): string => {
  if (!appKey) return 'Unknown App';

  // Handle special cases
  const specialCases: Record<string, string> = {
    'microsoft-outlook': 'Microsoft Outlook',
    'microsoft-teams': 'Microsoft Teams',
    'google-drive': 'Google Drive',
    'google-calendar': 'Google Calendar',
    'google-gmail': 'Gmail',
    salesforce: 'Salesforce',
    hubspot: 'HubSpot',
    slack: 'Slack',
    twilio: 'Twilio',
    zoom: 'Zoom',
    dropbox: 'Dropbox',
    onedrive: 'OneDrive',
    sharepoint: 'SharePoint',
  };

  if (specialCases[appKey.toLowerCase()]) {
    return specialCases[appKey.toLowerCase()];
  }

  // Default formatting: capitalize first letter of each word, replace hyphens/underscores with spaces
  return appKey
    .replace(/[-_]/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Sends OAuth result to parent window (for popup flow)
 * This function should be called on the redirect_uri page
 */
export const sendOAuthResultToParent = () => {
  const params = extractOAuthParams();

  if (window.opener) {
    if (params.error) {
      // Send error to parent window
      window.opener.postMessage(
        {
          type: 'OAUTH_ERROR',
          error:
            params.errorDescription || params.error || 'Authentication failed',
        },
        window.location.origin
      );
    } else if (params.state) {
      // Send success with state to parent window
      window.opener.postMessage(
        {
          type: 'OAUTH_SUCCESS',
          state: params.state,
          code: params.code,
          adminConsent: params.adminConsent,
        },
        window.location.origin
      );
    }

    // Close the popup window
    window.close();
  }
};

/**
 * Example usage for redirect_uri page:
 *
 * ```typescript
 * import { sendOAuthResultToParent } from './utils/oauthRedirectHandler';
 *
 * // Call this when the redirect page loads
 * useEffect(() => {
 *   sendOAuthResultToParent();
 * }, []);
 * ```
 */
