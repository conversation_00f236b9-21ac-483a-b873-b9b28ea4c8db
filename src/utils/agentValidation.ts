/**
 * Agent validation utilities to prevent race conditions and ensure data consistency
 */

export interface AgentValidationResult {
  isValid: boolean;
  error?: string;
  suggestedAgent?: string;
}

/**
 * Validates if an agent key is valid
 */
export const validateAgentKey = (
  agent<PERSON>ey: string | null
): AgentValidationResult => {
  if (!agent<PERSON>ey) {
    return {
      isValid: false,
      error: 'Agent key is required',
      suggestedAgent: 'regis',
    };
  }

  if (typeof agentKey !== 'string') {
    return {
      isValid: false,
      error: 'Agent key must be a string',
      suggestedAgent: 'regis',
    };
  }

  if (agentKey.trim().length === 0) {
    return {
      isValid: false,
      error: 'Agent key cannot be empty',
      suggestedAgent: 'regis',
    };
  }

  // Basic format validation
  if (!/^[a-zA-Z0-9_-]+$/.test(agentKey)) {
    return {
      isValid: false,
      error: 'Agent key contains invalid characters',
      suggestedAgent: 'regis',
    };
  }

  return { isValid: true };
};

/**
 * Validates if a request should proceed based on agent context
 */
export const validateRequestContext = (
  expectedAgent: string | null,
  currentAgent: string | null,
  isAgentSwitching: boolean
): AgentValidationResult => {
  // If agent is switching, requests should be queued
  if (isAgentSwitching) {
    return {
      isValid: false,
      error: 'Agent is currently switching, request should be queued',
    };
  }

  // If no expected agent, use current agent
  if (!expectedAgent) {
    if (!currentAgent) {
      return {
        isValid: false,
        error: 'No active agent available',
        suggestedAgent: 'regis',
      };
    }
    return { isValid: true };
  }

  // Validate expected agent
  const agentValidation = validateAgentKey(expectedAgent);
  if (!agentValidation.isValid) {
    return agentValidation;
  }

  // Check if expected agent matches current agent
  if (expectedAgent !== currentAgent) {
    return {
      isValid: false,
      error: `Agent mismatch: expected ${expectedAgent}, but current is ${currentAgent}`,
      suggestedAgent: currentAgent || 'regis',
    };
  }

  return { isValid: true };
};

/**
 * Sanitizes agent key to prevent injection attacks
 */
export const sanitizeAgentKey = (agentKey: string): string => {
  return agentKey.replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();
};

/**
 * Gets a fallback agent when the current agent is invalid
 */
export const getFallbackAgent = (invalidAgent?: string): string => {
  console.warn(`Using fallback agent due to invalid agent: ${invalidAgent}`);
  return 'regis';
};

/**
 * Validates agent headers in axios requests
 */
export const validateAgentHeaders = (
  headers: Record<string, any>,
  expectedAgent: string
): boolean => {
  const headerAgent = headers['X-Active-Agent'];

  if (!headerAgent) {
    console.warn('Missing X-Active-Agent header');
    return false;
  }

  if (headerAgent !== expectedAgent) {
    console.warn(
      `Agent header mismatch: expected ${expectedAgent}, got ${headerAgent}`
    );
    return false;
  }

  return true;
};

/**
 * Creates a timeout promise for agent operations
 */
export const createAgentTimeout = (
  timeoutMs: number = 5000
): Promise<never> => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Agent operation timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });
};

/**
 * Retries an agent operation with exponential backoff
 */
export const retryAgentOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries - 1) {
        break;
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      console.warn(
        `Agent operation failed (attempt ${attempt + 1}/${maxRetries}), retrying in ${delay}ms:`,
        error
      );

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
};

/**
 * Checks if an error is related to agent switching
 */
export const isAgentSwitchError = (error: any): boolean => {
  if (!error) return false;

  const errorMessage = error.message || error.toString();
  const agentSwitchKeywords = [
    'agent switch',
    'agent changed',
    'agent mismatch',
    'agent timeout',
    'agent not ready',
  ];

  return agentSwitchKeywords.some(keyword =>
    errorMessage.toLowerCase().includes(keyword)
  );
};
