import React, { createContext, useCallback, useContext, useState } from 'react';

import {
  AddNotificationOptions,
  NotificationContextValue,
  NotificationData,
  NotificationProviderProps,
} from '@/types/notifications';

/**
 * Context for managing notification state across the application
 */
const NotificationContext = createContext<NotificationContextValue | undefined>(
  undefined
);

/**
 * Generate a unique ID for notifications
 */
const generateNotificationId = (): string => {
  return `notification_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  maxNotifications = 5,
  autoRemoveDelay = 0, // 0 means no auto-removal
}) => {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Add a new notification to the stack
   */
  const addNotification = useCallback(
    (options: AddNotificationOptions): string => {
      // Form validation for required fields
      if (!options.message || options.message.trim() === '') {
        console.error(
          '[NotificationProvider] Message is required for notifications'
        );
        throw new Error('Notification message is required');
      }

      setIsLoading(true);

      const id = options.id || generateNotificationId();
      const newNotification: NotificationData = {
        id,
        message: options.message.trim(),
        additionalImage: options.additionalImage,
        additionalImageClassname: options.additionalImageClassname,
        activeAgent: options.activeAgent,
        createdAt: new Date(),
        isRemoving: false,
      };

      setNotifications(prev => {
        // Add new notification to the top (newest first)
        const updated = [newNotification, ...prev];

        // Enforce maximum notifications limit
        if (updated.length > maxNotifications) {
          // const removed = updated.slice(maxNotifications);
          // console.log(
          //   `[NotificationProvider] Removing ${removed.length} old notifications due to limit`
          // );
          return updated.slice(0, maxNotifications);
        }

        return updated;
      });

      setIsLoading(false);

      // Auto-removal if configured
      if (autoRemoveDelay > 0) {
        setTimeout(() => {
          removeNotification(id);
        }, autoRemoveDelay);
      }

      return id;
    },
    [maxNotifications, autoRemoveDelay]
  );

  /**
   * Remove a notification by ID
   */
  const removeNotification = useCallback((id: string) => {
    setIsLoading(true);

    // Mark notification as removing for exit animation
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, isRemoving: true }
          : notification
      )
    );

    // Remove after animation delay
    setTimeout(() => {
      setNotifications(prev => {
        const filtered = prev.filter(notification => notification.id !== id);
        return filtered;
      });
      setIsLoading(false);
    }, 300); // Match animation duration
  }, []);

  /**
   * Clear all notifications
   */
  const clearNotifications = useCallback(() => {
    setIsLoading(true);

    // Mark all as removing for smooth exit animations
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRemoving: true }))
    );

    // Clear after animation delay
    setTimeout(() => {
      setNotifications([]);
      setIsLoading(false);
    }, 300);
  }, [notifications.length]);

  const contextValue: NotificationContextValue = {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    isLoading,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

/**
 * Hook to access the notification context
 */
export const useNotificationContext = (): NotificationContextValue => {
  const context = useContext(NotificationContext);

  if (!context) {
    throw new Error(
      'useNotificationContext must be used within a NotificationProvider'
    );
  }

  return context;
};

/**
 * Convenience hook for adding notifications with simplified API
 */
export const useNotifications = () => {
  const {
    addNotification,
    removeNotification,
    clearNotifications,
    notifications,
    isLoading,
  } = useNotificationContext();

  /**
   * Add a simple notification with just a message
   */
  const notify = useCallback(
    (message: string) => {
      return addNotification({ message });
    },
    [addNotification]
  );

  /**
   * Add a notification with additional image (e.g., service logo)
   */
  const notifyWithImage = useCallback(
    (
      message: string,
      additionalImage: string,
      additionalImageClassname?: string
    ) => {
      return addNotification({
        message,
        additionalImage,
        additionalImageClassname,
      });
    },
    [addNotification]
  );

  /**
   * Add a notification with custom agent
   */
  const notifyWithAgent = useCallback(
    (message: string, agentName: string, avatar: string) => {
      return addNotification({
        message,
        activeAgent: { agentName, avatar },
      });
    },
    [addNotification]
  );

  return {
    // Core functions
    addNotification,
    removeNotification,
    clearNotifications,

    // Convenience methods
    notify,
    notifyWithImage,
    notifyWithAgent,

    // State
    notifications,
    isLoading,
    hasNotifications: notifications.length > 0,
    notificationCount: notifications.length,
  };
};
