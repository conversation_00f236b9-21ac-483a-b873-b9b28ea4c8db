import React, { createContext, ReactNode, useContext } from 'react';

import { BasicRegistrationPayload } from '@/types/user';

interface ISignupPayload extends BasicRegistrationPayload {
  password?: string;
  confirmPassword?: string;
}

interface IConversationSignupPayload {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
}

interface AuthEventContextType {
  resetPasswordLevel: number;
  conversationTemporaryUser: IConversationSignupPayload;
  email: { email: string };
  activateGetOtp: boolean;
  setResetPasswordLevelHandler: (value: React.SetStateAction<number>) => void;
  setActivateGetOtpHandler: (value: boolean) => void;
  setEmailHandler: (value: { email: string }) => void;
}

const AuthEventContext = createContext<AuthEventContextType | undefined>(
  undefined
);

export const useAuthEventContext = (): AuthEventContextType => {
  const context = useContext(AuthEventContext);
  if (!context) {
    throw new Error(
      'useAuthEventContext must be used within an AuthEventProvider'
    );
  }
  return context;
};

interface AuthEventProviderProps {
  children: ReactNode;
}

export const AuthEventProvider: React.FC<AuthEventProviderProps> = ({
  children,
}) => {
  // Import the hook logic directly here for now
  const [resetPasswordLevel, setResetPasswordLevel] = React.useState(1);
  const [conversationTemporaryUser, setConversationTemporaryUser] =
    React.useState<IConversationSignupPayload>({
      email: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
    });
  const [email, setEmail] = React.useState<{ email: string }>({ email: '' });
  const [activateGetOtp, setActivateGetOtp] = React.useState(false);

  const setResetPasswordLevelHandler = React.useCallback(
    (value: React.SetStateAction<number>) => setResetPasswordLevel(value),
    []
  );

  const setActivateGetOtpHandler = React.useCallback(
    (value: boolean) => setActivateGetOtp(value),
    []
  );
  const setEmailHandler = React.useCallback(
    (value: { email: string }) => setEmail(value),
    []
  );

  const value: AuthEventContextType = {
    setResetPasswordLevelHandler,
    resetPasswordLevel,
    setActivateGetOtpHandler,
    activateGetOtp,
    email,
    setEmailHandler,
    conversationTemporaryUser,
  };

  return (
    <AuthEventContext.Provider value={value}>
      {children}
    </AuthEventContext.Provider>
  );
};

export { IConversationSignupPayload, ISignupPayload };
