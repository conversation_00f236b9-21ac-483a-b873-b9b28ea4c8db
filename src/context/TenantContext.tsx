import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import type { ClaimedAgentSuite, TenantInfo } from '../types/user';
import {
  getFallbackAgent,
  sanitizeAgent<PERSON>ey,
  validateAgentKey,
} from '../utils/agentValidation';

type TenantContextType = {
  tenant: TenantInfo | null;
  tenantId: string | null;
  setTenantId: (tenantId: string | null) => void;
  activeAgent: string | null;
  setActiveAgent: (agentKey: string) => Promise<void>;
  isLoading: boolean;
  isAgentSwitching: boolean;
  pendingAgent: string | null;
  claimedSuites: ClaimedAgentSuite[];
  // Utility methods for request coordination
  waitForAgentReady: (agentKey?: string) => Promise<boolean>;
  isAgentReady: (agentKey?: string) => boolean;
};

const TenantContext = createContext<TenantContextType | undefined>(undefined);

const ACTIVE_AGENT_STORAGE_KEY = 'agentous_active_agent';
const TENANT_ID_STORAGE_KEY = 'agentous_tenant_id';

export const TenantProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [activeAgent, setActiveAgentState] = useState<string | null>('regis');
  const [storedTenantId, setStoredTenantId] = useState<string | null>(null);
  const [isReady, setIsReady] = useState<boolean>(false);
  const [isAgentSwitching, setIsAgentSwitching] = useState<boolean>(false);
  const [pendingAgent, setPendingAgent] = useState<string | null>(null);

  // Ref to track the current switching operation
  const switchingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const agentReadyResolversRef = useRef<
    Map<string, (success: boolean) => void>
  >(new Map());

  // Load stored values from localStorage once
  useEffect(() => {
    try {
      const storedAgent = localStorage.getItem(ACTIVE_AGENT_STORAGE_KEY);
      const storedTenant = localStorage.getItem(TENANT_ID_STORAGE_KEY);

      if (storedAgent) {
        setActiveAgentState(storedAgent);
      }
      if (storedTenant) {
        setStoredTenantId(storedTenant);
      }
    } catch (error) {
      console.error('Error loading data from localStorage:', error);
      localStorage.removeItem(ACTIVE_AGENT_STORAGE_KEY);
      localStorage.removeItem(TENANT_ID_STORAGE_KEY);
    } finally {
      setIsReady(true);
    }
  }, []);

  // For now, return null values for tenant and claimedSuites since we're simplifying
  const tenant: TenantInfo | null = null;
  const tenantId = storedTenantId;
  const claimedSuites: ClaimedAgentSuite[] = [];

  const setTenantId = (newTenantId: string | null) => {
    setStoredTenantId(newTenantId);
    try {
      if (newTenantId) {
        localStorage.setItem(TENANT_ID_STORAGE_KEY, newTenantId);
      } else {
        localStorage.removeItem(TENANT_ID_STORAGE_KEY);
      }
    } catch (error) {
      console.error('Error saving tenant ID to localStorage:', error);
    }
  };

  // Enhanced setActiveAgent with race condition prevention and validation
  const setActiveAgent = useCallback(
    async (agentKey: string): Promise<void> => {
      // Validate agent key
      const validation = validateAgentKey(agentKey);
      if (!validation.isValid) {
        const fallbackAgent =
          validation.suggestedAgent || getFallbackAgent(agentKey);
        console.warn(
          `Invalid agent key "${agentKey}": ${validation.error}. Using fallback: ${fallbackAgent}`
        );
        agentKey = fallbackAgent;
      }

      // Sanitize agent key
      const sanitizedAgentKey = sanitizeAgentKey(agentKey);
      if (sanitizedAgentKey !== agentKey) {
        console.warn(
          `Agent key sanitized from "${agentKey}" to "${sanitizedAgentKey}"`
        );
        agentKey = sanitizedAgentKey;
      }

      // Prevent switching to the same agent
      if (activeAgent === agentKey) {
        return;
      }

      // Clear any existing timeout
      if (switchingTimeoutRef.current) {
        clearTimeout(switchingTimeoutRef.current);
        switchingTimeoutRef.current = null;
      }

      // Set switching state
      setIsAgentSwitching(true);
      setPendingAgent(agentKey);

      try {
        // Update localStorage first (synchronous)
        if (agentKey) {
          localStorage.setItem(ACTIVE_AGENT_STORAGE_KEY, agentKey);
        } else {
          localStorage.removeItem(ACTIVE_AGENT_STORAGE_KEY);
        }

        // Update state
        setActiveAgentState(agentKey);

        // Wait for state to propagate (next tick)
        await new Promise(resolve => setTimeout(resolve, 0));

        // Resolve any waiting promises for this agent
        const resolver = agentReadyResolversRef.current.get(agentKey);
        if (resolver) {
          resolver(true);
          agentReadyResolversRef.current.delete(agentKey);
        }

        // Clear switching state after a brief delay to ensure axios instances are updated
        switchingTimeoutRef.current = setTimeout(() => {
          setIsAgentSwitching(false);
          setPendingAgent(null);
          switchingTimeoutRef.current = null;
        }, 100);

        // console.log(`Successfully switched to agent: ${agentKey}`);
      } catch (error) {
        console.error('Error saving active agent to localStorage:', error);

        // Resolve waiting promises with failure
        const resolver = agentReadyResolversRef.current.get(agentKey);
        if (resolver) {
          resolver(false);
          agentReadyResolversRef.current.delete(agentKey);
        }

        // Reset switching state
        setIsAgentSwitching(false);
        setPendingAgent(null);

        throw error;
      }
    },
    [activeAgent]
  );

  // Utility method to wait for agent to be ready
  const waitForAgentReady = useCallback(
    async (agentKey?: string): Promise<boolean> => {
      const targetAgent = agentKey || activeAgent;
      if (!targetAgent) return false;

      // If agent is already active and not switching, return immediately
      if (activeAgent === targetAgent && !isAgentSwitching) {
        return true;
      }

      // If switching to a different agent, wait for it
      return new Promise(resolve => {
        agentReadyResolversRef.current.set(targetAgent, resolve);

        // Timeout after 5 seconds
        setTimeout(() => {
          if (agentReadyResolversRef.current.has(targetAgent)) {
            agentReadyResolversRef.current.delete(targetAgent);
            resolve(false);
          }
        }, 5000);
      });
    },
    [activeAgent, isAgentSwitching]
  );

  // Utility method to check if agent is ready
  const isAgentReady = useCallback(
    (agentKey?: string): boolean => {
      const targetAgent = agentKey || activeAgent;
      return targetAgent === activeAgent && !isAgentSwitching;
    },
    [activeAgent, isAgentSwitching]
  );

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (switchingTimeoutRef.current) {
        clearTimeout(switchingTimeoutRef.current);
      }
      // Clear any pending resolvers
      agentReadyResolversRef.current.clear();
    };
  }, []);

  const value = useMemo<TenantContextType>(
    () => ({
      tenant,
      tenantId,
      setTenantId,
      activeAgent,
      setActiveAgent,
      isLoading: !isReady,
      isAgentSwitching,
      pendingAgent,
      claimedSuites,
      waitForAgentReady,
      isAgentReady,
    }),
    [
      tenant,
      tenantId,
      activeAgent,
      isReady,
      isAgentSwitching,
      pendingAgent,
      claimedSuites,
      setActiveAgent,
      waitForAgentReady,
      isAgentReady,
    ]
  );

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
};

export const useTenant = (): TenantContextType => {
  const ctx = useContext(TenantContext);
  if (!ctx) throw new Error('useTenant must be used within a TenantProvider');
  return ctx;
};
