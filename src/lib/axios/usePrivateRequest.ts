import { useKeycloak } from '@react-keycloak/web';
import type { AxiosInstance, AxiosRequestConfig } from 'axios';
import axios from 'axios';
import { useCallback, useEffect, useRef } from 'react';

import { isDevEnvironment } from '@/utils/helpers';

// Request queue for managing requests during agent transitions
interface QueuedRequest {
  config: AxiosRequestConfig;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  expectedAgent: string;
}

export const usePrivateRequest = (baseURL: string) => {
  const axiosInstance = useRef<AxiosInstance>();
  const { keycloak, initialized } = useKeycloak();

  const kcToken = keycloak?.token ?? import.meta.env.VITE_DEV_ACCESS_TOKEN;

  useEffect(() => {
    axiosInstance.current = axios.create({
      baseURL,
      headers: {
        Authorization:
          initialized || isDevEnvironment() ? `Bearer ${kcToken}` : undefined,
      },
      timeout: 120000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });

    axiosInstance.current.defaults.headers.common['Save-Data'] = 'on';

    axiosInstance.current.interceptors.response.use(
      response => response,
      async error => {
        if (error.code === 'ECONNABORTED') {
          console.warn('Request timed out. Retrying...');
          return axiosInstance.current?.request(error.config); // Retry
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axiosInstance.current = undefined;
    };
  }, [baseURL, initialized, kcToken]);

  return axiosInstance;
};

export const usePivotlPrivateRequest = (
  baseURL: string,
  tenentId?: string,
  agent?: string
) => {
  const axiosInstance = useRef<AxiosInstance>();
  const requestQueue = useRef<QueuedRequest[]>([]);
  const isProcessingQueue = useRef<boolean>(false);
  const { keycloak, initialized } = useKeycloak();

  const kcToken = keycloak?.token ?? import.meta.env.VITE_DEV_ACCESS_TOKEN;

  // Process queued requests
  const processQueue = useCallback(async () => {
    if (isProcessingQueue.current || requestQueue.current.length === 0) {
      return;
    }

    isProcessingQueue.current = true;
    const queue = [...requestQueue.current];
    requestQueue.current = [];

    for (const queuedRequest of queue) {
      try {
        // Validate that the expected agent matches current agent
        if (queuedRequest.expectedAgent === agent && axiosInstance.current) {
          const response = await axiosInstance.current.request(
            queuedRequest.config
          );
          queuedRequest.resolve(response);
        } else {
          // Agent has changed, reject the request
          queuedRequest.reject(new Error('Agent changed during request queue'));
        }
      } catch (error) {
        queuedRequest.reject(error);
      }
    }

    isProcessingQueue.current = false;
  }, [agent]);

  useEffect(() => {
    // Build headers conditionally to avoid sending empty headers which cause server errors
    const headers: Record<string, any> = {};

    if (initialized || isDevEnvironment()) {
      headers.Authorization = `Bearer ${kcToken}`;
    }

    if (tenentId) {
      headers['X-Active-Tenant'] = tenentId;
    }

    if (agent) {
      headers['X-Active-Agent'] = agent;
    }

    // Create new axios instance
    const newInstance = axios.create({
      baseURL,
      headers,
      timeout: 120000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });

    newInstance.defaults.headers.common['Save-Data'] = 'on';

    // Add request interceptor to validate agent
    newInstance.interceptors.request.use(
      config => {
        // Validate that the agent header matches expected agent
        const requestAgent = config.headers?.['X-Active-Agent'];
        if (agent && requestAgent !== agent) {
          console.warn(
            `Agent mismatch detected. Expected: ${agent}, Got: ${requestAgent}`
          );
          // Update header to current agent
          if (config.headers) {
            config.headers['X-Active-Agent'] = agent;
          }
        }
        return config;
      },
      error => Promise.reject(error)
    );

    newInstance.interceptors.response.use(
      response => response,
      async error => {
        if (error.code === 'ECONNABORTED') {
          console.warn('Request timed out. Retrying...');
          return newInstance?.request(error.config); // Retry
        }
        return Promise.reject(error);
      }
    );

    axiosInstance.current = newInstance;

    // Process any queued requests after instance is ready
    setTimeout(processQueue, 0);

    return () => {
      axiosInstance.current = undefined;
    };
  }, [baseURL, initialized, kcToken, tenentId, agent, processQueue]);

  return axiosInstance;
};

// Enhanced hook for unauthenticated requests with agent validation
export const useUnauthenticatedAgentRequest = (
  baseURL: string,
  agent: string
) => {
  const axiosInstance = useRef<AxiosInstance>();

  useEffect(() => {
    const newInstance = axios.create({
      baseURL,
      headers: {
        'X-Active-Agent': agent,
      },
      timeout: 120000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });

    newInstance.defaults.headers.common['Save-Data'] = 'on';

    // Add request interceptor to validate agent
    newInstance.interceptors.request.use(
      config => {
        // Ensure agent header is current
        if (config.headers) {
          config.headers['X-Active-Agent'] = agent;
        }
        return config;
      },
      error => Promise.reject(error)
    );

    newInstance.interceptors.response.use(
      response => response,
      async error => {
        if (error.code === 'ECONNABORTED') {
          console.warn('Request timed out. Retrying...');
          return newInstance?.request(error.config); // Retry
        }
        return Promise.reject(error);
      }
    );

    axiosInstance.current = newInstance;

    return () => {
      axiosInstance.current = undefined;
    };
  }, [baseURL, agent]);

  return axiosInstance;
};
