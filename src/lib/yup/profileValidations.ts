import * as yup from 'yup';

import { ProfileFormData } from '../../types/profile';

export const profileSettingsSchema: yup.ObjectSchema<ProfileFormData> = yup
  .object()
  .shape({
    firstName: yup
      .string()
      .required('First name is required')
      .min(2, 'First name must be at least 2 characters')
      .max(50, 'First name cannot exceed 50 characters')
      .matches(
        /^[a-zA-Z\s]+$/,
        'First name can only contain letters and spaces'
      ),

    lastName: yup
      .string()
      .required('Last name is required')
      .min(2, 'Last name must be at least 2 characters')
      .max(50, 'Last name cannot exceed 50 characters')
      .matches(
        /^[a-zA-Z\s]+$/,
        'Last name can only contain letters and spaces'
      ),

    email: yup
      .string()
      .required('Email is required')
      .email('Please enter a valid email address')
      .max(100, 'Email cannot exceed 100 characters'),

    role: yup.string().required('Role is required'),

    timezone: yup.string().required('Timezone is required'),
  });
