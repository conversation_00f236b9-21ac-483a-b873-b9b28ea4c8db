import { useMutation } from '@tanstack/react-query';

import userProfileService from '@/services/userProfileService';

interface ResetPasswordPayload {
  token: string;
  password: string;
  confirmPassword: string;
}

interface ForgotPasswordPayload {
  email: string;
}

export const useResetPassword = () => {
  return useMutation({
    mutationFn: async (payload: ResetPasswordPayload) => {
      const { token, password } = payload;
      return userProfileService.resetPassword({
        token,
        newPassword: password,
      });
    },
  });
};

export const useGetLinkForPasswordReset = () => {
  return useMutation({
    mutationFn: async (payload: ForgotPasswordPayload) => {
      return userProfileService.requestPasswordReset(payload);
    },
  });
};

export const useRequestEmailVerification = () => {
  return useMutation({
    mutationFn: async (payload: { email: string }) => {
      return userProfileService.requestEmailVerification(payload);
    },
  });
};
