import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useDashboardService } from '@/services/dashboardService';
import {
  CreateDailyInsightRequest,
  DailyInsightFilter,
} from '@/types/dashboard';

export const DASHBOARD_QUERY_KEYS = {
  all: ['daily-insights'] as const,
  lists: () => [...DASHBOARD_QUERY_KEYS.all, 'list'] as const,
  list: (filter: DailyInsightFilter) =>
    [...DASHBOARD_QUERY_KEYS.lists(), filter] as const,
};

export const useDailyInsightsList = (
  filter: DailyInsightFilter,
  enabled = true
) => {
  const { getDailyInsights } = useDashboardService();
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.list(filter),
    queryFn: () => getDailyInsights(filter),
    enabled: enabled,
    select: data => data,
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

export const useCreateDailyInsightMutation = () => {
  const queryClient = useQueryClient();
  const { createDailyInsight } = useDashboardService();

  return useMutation({
    mutationFn: (payload: CreateDailyInsightRequest) =>
      createDailyInsight(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: DASHBOARD_QUERY_KEYS.lists(),
      });
    },
    onError: error => {
      console.error('Failed to create daily insight:', error);
    },
  });
};
