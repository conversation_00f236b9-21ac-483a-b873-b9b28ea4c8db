import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { createAuthenticatedAxiosInstance } from '@/helpers/axiosConfig';
import membersService from '@/services/membersService';
import type {
  ApiInvitesListResponse,
  ApiMembersListResponse,
  ApiStandardResponse,
  ClaimAgentSuiteRequest,
  InviteSuiteMemberRequest,
  UpdateSuiteInviteRoleRequest,
  UpdateSuiteMemberRoleRequest,
} from '@/types/members';

const getKeycloakToken = (): string | undefined => {
  return import.meta.env.VITE_DEV_ACCESS_TOKEN;
};

// Query keys for React Query cache management
export const MEMBERS_QUERY_KEYS = {
  all: ['suite-members'] as const,
  lists: () => [...MEMBERS_QUERY_KEYS.all, 'list'] as const,
  list: (filters: object) => [...MEMBERS_QUERY_KEYS.lists(), filters] as const,
  invitesAll: ['suite-invites'] as const,
  invitesLists: () => [...MEMBERS_QUERY_KEYS.invitesAll, 'list'] as const,
  invitesList: (filters: object) =>
    [...MEMBERS_QUERY_KEYS.invitesLists(), filters] as const,
};

// Members list
export const useSuiteMembers = (
  agentSuiteKey: string,
  searchQuery: string,
  page: number,
  pageSize: number,
  enabled = true
) => {
  const authToken = createAuthenticatedAxiosInstance();
  const filters = { agentSuiteKey, searchQuery, page, pageSize };
  return useQuery({
    queryKey: MEMBERS_QUERY_KEYS.list(filters),
    queryFn: () => membersService.getSuiteMembers(authToken, filters),
    enabled: enabled && !!agentSuiteKey && !!authToken,
    select: (data: ApiMembersListResponse) => data.data,
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Invites list
export const useSuiteInvites = (
  agentSuiteKey: string,
  searchQuery: string,
  page: number,
  pageSize: number,
  enabled = true
) => {
  const authToken = createAuthenticatedAxiosInstance();
  const filters = { agentSuiteKey, searchQuery, page, pageSize };
  return useQuery({
    queryKey: MEMBERS_QUERY_KEYS.invitesList(filters),
    queryFn: () => membersService.getSuiteInvites(authToken, filters),
    enabled: enabled && !!agentSuiteKey && !!authToken,
    select: (data: ApiInvitesListResponse) => data.data,
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Invite new suite member
export const useInviteSuiteMemberMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  return useMutation({
    mutationFn: (
      payload: InviteSuiteMemberRequest
    ): Promise<ApiStandardResponse<string>> =>
      membersService.inviteSuiteMember(authToken, payload),
    onSuccess: (_data, variables) => {
      // Invalidate lists for this agent suite key
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.invitesList({
          agentSuiteKey: variables.agentSuiteKey,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.list({
          agentSuiteKey: variables.agentSuiteKey,
        }),
      });
    },
    onError: (_error, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.invitesList({
          agentSuiteKey: variables.agentSuiteKey,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.list({
          agentSuiteKey: variables.agentSuiteKey,
        }),
      });
    },
  });
};

// Update suite member role
export const useUpdateSuiteMemberRoleMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  return useMutation({
    mutationFn: ({
      memberId,
      payload,
    }: {
      memberId: string;
      payload: UpdateSuiteMemberRoleRequest;
    }): Promise<ApiStandardResponse<string>> =>
      membersService.updateSuiteMemberRole(authToken, memberId, payload),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.list({
          agentSuiteKey: variables.payload.agentSuiteKey,
        }),
      });
    },
    onError: (_error, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.list({
          agentSuiteKey: variables.payload.agentSuiteKey,
        }),
      });
    },
  });
};

// Update suite invite role
export const useUpdateSuiteInviteRoleMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  return useMutation({
    mutationFn: ({
      inviteId,
      payload,
    }: {
      inviteId: string;
      payload: UpdateSuiteInviteRoleRequest;
    }): Promise<ApiStandardResponse<string>> =>
      membersService.updateSuiteInviteRole(authToken, inviteId, payload),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.invitesList({
          agentSuiteKey: variables.payload.agentSuiteKey,
        }),
      });
    },
    onError: (_error, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.invitesList({
          agentSuiteKey: variables.payload.agentSuiteKey,
        }),
      });
    },
  });
};

// Remove suite member
export const useRemoveSuiteMemberMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  return useMutation({
    mutationFn: ({
      memberId,
      agentSuiteKey,
    }: {
      memberId: string;
      agentSuiteKey: string;
    }): Promise<ApiStandardResponse<string>> =>
      membersService.removeSuiteMember(authToken, memberId, agentSuiteKey),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.list({
          agentSuiteKey: variables.agentSuiteKey,
        }),
      });
    },
    onError: (_error, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.list({
          agentSuiteKey: variables.agentSuiteKey,
        }),
      });
    },
  });
};

// Cancel suite invite
export const useCancelSuiteInviteMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();
  return useMutation({
    mutationFn: ({
      inviteId,
      agentSuiteKey,
    }: {
      inviteId: string;
      agentSuiteKey: string;
    }): Promise<ApiStandardResponse<string>> =>
      membersService.cancelSuiteInvite(authToken, inviteId, agentSuiteKey),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.invitesList({
          agentSuiteKey: variables.agentSuiteKey,
        }),
      });
    },
    onError: (_error, variables) => {
      queryClient.invalidateQueries({
        queryKey: MEMBERS_QUERY_KEYS.invitesList({
          agentSuiteKey: variables.agentSuiteKey,
        }),
      });
    },
  });
};

// Claim agent suite
export const useClaimAgentSuiteMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();
  return useMutation({
    mutationFn: (payload: ClaimAgentSuiteRequest) =>
      membersService.claimAgentSuite(authToken, payload),
    onSuccess: (_data, variables) => {
      if (variables?.data) {
        queryClient.invalidateQueries({
          queryKey: MEMBERS_QUERY_KEYS.list({ agentSuiteKey: variables.data }),
        });
        queryClient.invalidateQueries({
          queryKey: MEMBERS_QUERY_KEYS.invitesList({
            agentSuiteKey: variables.data,
          }),
        });
      }
    },
    onError: (_error, variables) => {
      if (variables?.data) {
        queryClient.invalidateQueries({
          queryKey: MEMBERS_QUERY_KEYS.list({ agentSuiteKey: variables.data }),
        });
        queryClient.invalidateQueries({
          queryKey: MEMBERS_QUERY_KEYS.invitesList({
            agentSuiteKey: variables.data,
          }),
        });
      }
    },
  });
};
