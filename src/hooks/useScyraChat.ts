import moment from 'moment';
import { useCallback, useEffect, useRef, useState } from 'react';

import { useTenant } from '@/context/TenantContext';

import { useAuth } from '../context/AuthContext';
import { generateSecureSessionId } from '../services/businessStackService';
import {
  filterRequestFieldFromMessage,
  useScyraChatApi,
  useScyraChatHistoryApi,
} from '../services/scyraChatService';
import { ChatHistoryItem, MessageSender } from '../types/agents';
import { ScyraChatState, ScyraMessage } from '../types/businessStack';
import { useConversationState } from './useConversationState';

export const useScyraChat = (
  externalMessage?: string,
  externalInjectedMessage?: string
) => {
  const { user } = useAuth();
  const {
    isLoading: isActiveTenantLoading,
    activeAgent,
    waitForAgentReady,
  } = useTenant();
  const [state, setState] = useState<ScyraChatState>({
    messages: [],
    isLoading: false,
    sessionId: generateSecureSessionId(),
  });

  const chatWithScyra = useScyraChatApi();
  const fetchChatHistory = useScyraChatHistoryApi();

  const getWelcomeMessage = useCallback((): string => {
    if (user?.firstName) {
      return `Hi ${user.firstName}, What would you like to do today?`;
    }
    return `Hi, What would you like to do today?`;
  }, [user?.firstName]);

  const capitalizeAgentName = useCallback((key: string) => {
    if (!key) return 'Agent';
    return key.charAt(0).toUpperCase() + key.slice(1);
  }, []);

  // Function to add external error messages to chat
  const addInjectedMessage = useCallback(
    (injectedMessage: string) => {
      const injectedMsg: ScyraMessage = {
        id: `error-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        sender: activeAgent || '',
        content: injectedMessage,
        timestamp: new Date(),
        senderName: capitalizeAgentName(activeAgent || ''),
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, injectedMsg],
      }));
    },
    [activeAgent]
  );

  // Handle external injected messages
  useEffect(() => {
    if (externalInjectedMessage && externalInjectedMessage.trim()) {
      addInjectedMessage(externalInjectedMessage);
    }
  }, [externalInjectedMessage, addInjectedMessage]);

  // Helper to map API history to ScyraMessage
  const mapHistoryToScyraMessage = useCallback(
    (item: ChatHistoryItem): ScyraMessage => {
      const senderKey = item.sender === activeAgent ? activeAgent : 'user';
      return {
        id: `${senderKey}-${item.createdAt}`,
        sender: senderKey,
        content: filterRequestFieldFromMessage(item.message),
        timestamp: new Date(item.createdAt),
        senderName:
          senderKey === 'user' ? 'You' : capitalizeAgentName(activeAgent || ''),
      };
    },
    [activeAgent, capitalizeAgentName]
  );

  // On mount, load chat history and add welcome message if no history
  // Load or reload chat history when tenant/agent is ready or when activeAgent changes
  useEffect(() => {
    if (isActiveTenantLoading) return; // wait until tenant/agent are initialized

    const loadHistory = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true }));
        const history = await fetchChatHistory();
        let messages: ScyraMessage[] = [];
        if (history && history.length > 0) {
          messages = history
            .sort(
              (a, b) =>
                new Date(a.createdAt).getTime() -
                new Date(b.createdAt).getTime()
            )
            .map(mapHistoryToScyraMessage);
        }

        // Always add welcome message last if no history
        if (messages.length === 0) {
          messages.push({
            id: 'welcome-message',
            sender: activeAgent || '',
            content: getWelcomeMessage(),
            timestamp: new Date(),
            senderName: capitalizeAgentName(activeAgent || ''),
          });
        }
        // Reset session id when switching agents
        setState(
          prev =>
            ({
              ...prev,
              messages,
              isLoading: false,
              sessionId: generateSecureSessionId(),
            }) as ScyraChatState
        );
      } catch (error) {
        // fallback to welcome message
        setState(
          prev =>
            ({
              ...prev,
              isLoading: false,
              messages: [
                {
                  id: 'welcome-message',
                  sender: activeAgent || '',
                  content: getWelcomeMessage(),
                  timestamp: new Date(),
                  senderName: capitalizeAgentName(activeAgent || ''),
                },
              ],
            }) as ScyraChatState
        );
      }
    };

    loadHistory();
  }, [isActiveTenantLoading, activeAgent]);

  const sendMessage = useCallback(
    async (messageContent: string) => {
      if (!messageContent.trim() || state.isLoading) return;

      // Prevent sending while tenant/agent data is still loading
      if (isActiveTenantLoading) {
        // Add a local message informing user to wait
        setState(
          prev =>
            ({
              ...prev,
              messages: [
                ...prev.messages,
                {
                  id: `error-${Date.now()}`,
                  sender: activeAgent || '',
                  content:
                    'Agent is still initializing. Please wait a moment and try again.',
                  timestamp: new Date(),
                  senderName: capitalizeAgentName(activeAgent || ''),
                },
              ],
            }) as ScyraChatState
        );
        return;
      }

      // Add user message
      const userMessage: ScyraMessage = {
        id: `user-${Date.now()}`,
        sender: 'user',
        content: messageContent.trim(),
        timestamp: new Date(),
        senderName: 'You',
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        isLoading: true,
      }));

      try {
        // Call Scyra API
        const response = await chatWithScyra({
          userMessage: messageContent.trim(),
          sessionId: state.sessionId,
        });

        // Add agent response (with additional filtering for safety)
        const agentMessage: ScyraMessage = {
          id: `${activeAgent}-${Date.now()}`,
          sender: activeAgent || '',
          content: filterRequestFieldFromMessage(response),
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent || ''),
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, agentMessage],
          isLoading: false,
        }));
      } catch (error) {
        console.error(`Error sending message to ${activeAgent}:`, error);

        // Add error message
        const errorMessage: ScyraMessage = {
          id: `error-${Date.now()}`,
          sender: activeAgent || '',
          content: 'Sorry, I encountered an error. Please try again.',
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent || ''),
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, errorMessage],
          isLoading: false,
        }));
      }
    },
    [chatWithScyra, state.sessionId, state.isLoading]
  );

  // Track auto-send to prevent duplicates
  const hasAutoSentExternalMessage = useRef(false);

  // Handle external messages (from HeroSection)
  const handleExternalMessage = useCallback(async () => {
    if (
      externalMessage &&
      externalMessage.trim() &&
      !hasAutoSentExternalMessage.current
    ) {
      // Double-check that we're targeting the correct agent
      if (activeAgent !== 'regis') {
        console.warn(
          `Agent mismatch before auto-send: expected regis, current ${activeAgent}. Waiting...`
        );
        return; // Don't send yet, wait for agent to be correct
      }

      // Wait for agent to be fully ready
      const isReady = await waitForAgentReady('regis');
      if (!isReady) {
        console.error('Agent not ready for auto-send, skipping message');
        return;
      }

      // Final check after waiting
      if (activeAgent === 'regis') {
        hasAutoSentExternalMessage.current = true;
        sendMessage(externalMessage);
      } else {
        console.error(
          `Agent still mismatched after waiting: expected regis, got ${activeAgent}`
        );
      }
    }
  }, [externalMessage, sendMessage, activeAgent, waitForAgentReady]);

  useEffect(() => {
    handleExternalMessage();
    if (externalMessage && externalMessage.trim()) {
      hasAutoSentExternalMessage.current = true;
    }
  }, [handleExternalMessage, externalMessage]);

  // Group messages by date (YYYY-MM-DD)
  const groupMessagesByDate = () => {
    const grouped: Record<string, ScyraMessage[]> = {};
    const sorted = [...state.messages].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );
    sorted.forEach(msg => {
      const dateKey = moment(msg.timestamp).format('YYYY-MM-DD');
      if (!grouped[dateKey]) grouped[dateKey] = [];
      grouped[dateKey].push(msg);
    });
    return grouped;
  };

  // Method to manually reload chat history (for agent switching)
  const reloadChatHistory = useCallback(async () => {
    if (isActiveTenantLoading) return;

    try {
      setState(prev => ({ ...prev, isLoading: true }));
      const history = await fetchChatHistory();
      let messages: ScyraMessage[] = [];
      if (history && history.length > 0) {
        messages = history
          .sort(
            (a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          )
          .map(mapHistoryToScyraMessage);
      }

      // Always add welcome message last if no history
      if (messages.length === 0) {
        messages.push({
          id: 'welcome-message',
          sender: activeAgent || '',
          content: getWelcomeMessage(),
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent || 'scyra'),
        });
      }
      // Reset session id when switching agents
      setState(
        prev =>
          ({
            ...prev,
            messages,
            isLoading: false,
            sessionId: generateSecureSessionId(),
          }) as ScyraChatState
      );
    } catch (error) {
      // fallback to welcome message
      setState(
        prev =>
          ({
            ...prev,
            isLoading: false,
            messages: [
              {
                id: 'welcome-message',
                sender: activeAgent,
                content: getWelcomeMessage(),
                timestamp: new Date(),
                senderName: capitalizeAgentName(activeAgent || 'scyra'),
              },
            ],
          }) as ScyraChatState
      );
    }
  }, [
    isActiveTenantLoading,
    fetchChatHistory,
    mapHistoryToScyraMessage,
    getWelcomeMessage,
    activeAgent,
    capitalizeAgentName,
  ]);

  return {
    state,
    sendMessage,
    groupMessagesByDate,
    reloadChatHistory,
    addInjectedMessage,
  };
};

export const useScyraChatHistory = () => {
  const fetchChatHistory = useScyraChatHistoryApi();
  const { addMessage } = useConversationState();
  const { activeAgent } = useTenant();

  // Converts API history item to ChatMessage format
  const mapHistoryToChatMessage = (item: ChatHistoryItem) => {
    const sender: MessageSender =
      item.sender === activeAgent ? activeAgent : 'user';
    return {
      id: `${sender}-${item.createdAt}`,
      sender,
      content: filterRequestFieldFromMessage(item.message),
      timestamp: new Date(item.createdAt),
      senderName: sender === 'user' ? 'You' : activeAgent,
    };
  };

  // Loads and adds chat history to state, sorted by date ascending
  const loadChatHistory = useCallback(async () => {
    const history = await fetchChatHistory();
    // Sort by createdAt ascending
    const sorted = history.sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
    // Add each message to state
    sorted.forEach(item => {
      if (!activeAgent) return;
      const msg = mapHistoryToChatMessage(item);
      addMessage(msg.sender, msg.content, msg?.senderName || '');
    });
  }, [fetchChatHistory, addMessage]);

  return { loadChatHistory };
};
