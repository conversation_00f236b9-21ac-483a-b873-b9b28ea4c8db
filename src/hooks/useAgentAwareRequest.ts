import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useCallback, useRef } from 'react';

import { useTenant } from '@/context/TenantContext';

/**
 * Hook to make agent-aware requests that handle agent switching states
 * This ensures requests are only made when the agent is ready and validates agent context
 */
export const useAgentAwareRequest = (
  axiosInstanceRef: React.RefObject<AxiosInstance>
) => {
  const { activeAgent, isAgentSwitching, waitForAgentReady, isAgentReady } =
    useTenant();
  const pendingRequestsRef = useRef<Set<AbortController>>(new Set());

  // Make a request with agent validation and switching awareness
  const makeRequest = useCallback(
    async <T = any>(
      config: AxiosRequestConfig,
      options: {
        requireAgent?: boolean;
        expectedAgent?: string;
        timeout?: number;
      } = {}
    ): Promise<AxiosResponse<T>> => {
      const { requireAgent = true, expectedAgent, timeout = 5000 } = options;

      // If agent is required but not available, reject
      if (requireAgent && !activeAgent) {
        throw new Error('No active agent available for request');
      }

      // If a specific agent is expected, validate it
      if (expectedAgent && activeAgent !== expectedAgent) {
        throw new Error(
          `Expected agent ${expectedAgent}, but active agent is ${activeAgent}`
        );
      }

      // Wait for agent to be ready if switching
      if (isAgentSwitching || !isAgentReady(expectedAgent)) {
        const isReady = await waitForAgentReady(expectedAgent);
        if (!isReady) {
          throw new Error('Agent switch timeout - request cancelled');
        }
      }

      // Ensure axios instance is available
      if (!axiosInstanceRef.current) {
        throw new Error('Axios instance not ready');
      }

      // Create abort controller for this request
      const abortController = new AbortController();
      pendingRequestsRef.current.add(abortController);

      try {
        // Add abort signal to config
        const requestConfig: AxiosRequestConfig = {
          ...config,
          signal: abortController.signal,
        };

        // Validate agent header one more time before request
        if (requireAgent && activeAgent) {
          requestConfig.headers = {
            ...requestConfig.headers,
            'X-Active-Agent': activeAgent,
          };
        }

        const response =
          await axiosInstanceRef.current.request<T>(requestConfig);

        // Remove from pending requests
        pendingRequestsRef.current.delete(abortController);

        return response;
      } catch (error) {
        // Remove from pending requests
        pendingRequestsRef.current.delete(abortController);

        // If request was aborted due to agent switch, provide better error message
        if (abortController.signal.aborted) {
          throw new Error('Request cancelled due to agent switch');
        }

        throw error;
      }
    },
    [
      activeAgent,
      isAgentSwitching,
      waitForAgentReady,
      isAgentReady,
      axiosInstanceRef,
    ]
  );

  // Cancel all pending requests (useful when agent switches)
  const cancelPendingRequests = useCallback(() => {
    pendingRequestsRef.current.forEach(controller => {
      controller.abort();
    });
    pendingRequestsRef.current.clear();
  }, []);

  // Check if requests can be made safely
  const canMakeRequest = useCallback(
    (expectedAgent?: string): boolean => {
      return (
        !isAgentSwitching &&
        isAgentReady(expectedAgent) &&
        !!axiosInstanceRef.current
      );
    },
    [isAgentSwitching, isAgentReady, axiosInstanceRef]
  );

  return {
    makeRequest,
    cancelPendingRequests,
    canMakeRequest,
    activeAgent,
    isAgentSwitching,
  };
};

/**
 * Utility function to create agent-aware API methods
 */
export const createAgentAwareApiMethod = <TParams extends any[], TResponse>(
  axiosInstanceRef: React.RefObject<AxiosInstance>,
  requestFn: (instance: AxiosInstance, ...params: TParams) => Promise<TResponse>
) => {
  return (...params: TParams): Promise<TResponse> => {
    if (!axiosInstanceRef.current) {
      throw new Error('Axios instance not ready');
    }
    return requestFn(axiosInstanceRef.current, ...params);
  };
};
