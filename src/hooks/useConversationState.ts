import { useCallback, useState } from 'react';

import { generateSecureSessionId } from '../services/upivotalAgenticService';
import {
  ChatMessage,
  ConversationState,
  MessageSender,
  OnboardingProgress,
  OnboardingStep,
} from '../types/agents';

const initialOnboardingProgress: OnboardingProgress = {
  isSignupStarted: false,
  isSignupCompleted: false,
  currentStep: 'initial',
};

const initialState: ConversationState = {
  messages: [],
  onboardingProgress: initialOnboardingProgress,
  isLoading: false,
  showSignupButton: false,
  showProceedToDashboardButton: false,
  sessionId: generateSecureSessionId(),
};

/**
 * Parses metadata from Scyra's response
 * @param message - The full message from Scyra
 * @returns Parsed metadata object or null if no metadata found
 */
const parseMetadata = (message: string): Record<string, any> | null => {
  try {
    // Look for metaData: line in the message
    const metaDataMatch = message.match(/metaData:\s*({[\s\S]*?})/);
    if (metaDataMatch && metaDataMatch[1]) {
      return JSON.parse(metaDataMatch[1]);
    }
  } catch (error) {
    console.error('Failed to parse metadata:', error);
  }
  return null;
};

export const useConversationState = () => {
  const [state, setState] = useState<ConversationState>(initialState);

  const addMessage = useCallback(
    (sender: MessageSender, content: string, senderName: string) => {
      const newMessage: ChatMessage = {
        id: Date.now().toString(),
        sender,
        content,
        timestamp: new Date(),
        senderName,
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, newMessage],
        // Show signup button only after Scyra messages (not user messages)
        showSignupButton:
          sender === 'scyra' && !prev.onboardingProgress.isSignupStarted,
      }));
    },
    []
  );

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const startSignup = useCallback(() => {
    setState(prev => ({
      ...prev,
      onboardingProgress: {
        ...prev.onboardingProgress,
        isSignupStarted: true,
        currentStep: 'firstName',
      },
      showSignupButton: false,
    }));
  }, []);

  const updateOnboardingProgress = useCallback(
    (
      field: keyof OnboardingProgress,
      value: string | boolean | OnboardingStep
    ) => {
      setState(prev => ({
        ...prev,
        onboardingProgress: {
          ...prev.onboardingProgress,
          [field]: value,
        },
      }));
    },
    []
  );

  const extractOnboardingDataFromMessage = useCallback(
    (message: string) => {
      // Parse metadata from Scyra's response
      // This is the ONLY source of truth for user data (firstName, lastName, email, emailVerified)
      // User input is NOT used for these fields - only Scyra's metadata
      const metadata = parseMetadata(message);

      if (metadata) {
        // Update onboarding progress based on metadata fields ONLY
        // Do not extract any user data from user input - only from Scyra's metadata
        setState(prev => {
          const updatedProgress = { ...prev.onboardingProgress };

          // Extract user data ONLY from Scyra's metadata
          if (metadata.firstName !== undefined) {
            updatedProgress.firstName = metadata.firstName;
          }
          if (metadata.lastName !== undefined) {
            updatedProgress.lastName = metadata.lastName;
          }
          if (metadata.email !== undefined) {
            updatedProgress.email = metadata.email;
          }
          if (metadata.emailVerified === true) {
            updatedProgress.emailVerified = true;
          }

          // Check if signup should be completed:
          // - User was in password step AND Scyra responded with ANY metadata
          // - This indicates Scyra has processed the password and confirmed account creation
          if (prev.onboardingProgress.currentStep === 'password' && metadata) {
            updatedProgress.isSignupCompleted = true;
            updatedProgress.currentStep = 'completed';
          }

          return {
            ...prev,
            onboardingProgress: updatedProgress,
            showProceedToDashboardButton:
              updatedProgress.isSignupCompleted === true,
          };
        });
      }

      // Fallback to text-based detection for step progression
      const lowerMessage = message.toLowerCase();

      if (
        lowerMessage.includes('first name') ||
        lowerMessage.includes('what is your first name')
      ) {
        updateOnboardingProgress('currentStep', 'firstName');
      } else if (
        lowerMessage.includes('last name') ||
        lowerMessage.includes('what is your last name')
      ) {
        updateOnboardingProgress('currentStep', 'lastName');
      } else if (
        lowerMessage.includes('password') ||
        lowerMessage.includes('enter your password')
      ) {
        updateOnboardingProgress('currentStep', 'password');
      } else if (
        lowerMessage.includes('verification code') ||
        lowerMessage.includes('verification email') ||
        lowerMessage.includes('inbox') ||
        lowerMessage.includes('verification')
      ) {
        updateOnboardingProgress('currentStep', 'verification');
      } else if (
        lowerMessage.includes('your email address') ||
        lowerMessage.includes('what is your email')
      ) {
        updateOnboardingProgress('currentStep', 'email');
      }
    },
    [updateOnboardingProgress]
  );

  const extractUserDataFromUserMessage = useCallback(
    (message: string, currentStep: OnboardingStep) => {
      // Only extract verification code and password from user messages
      // All other data (firstName, lastName, email) must come from Scyra's metadata
      switch (currentStep) {
        case 'verification':
          updateOnboardingProgress('verificationCode', message);
          break;
        case 'password':
          updateOnboardingProgress('password', message);
          break;
        default:
          break;
      }
    },
    [updateOnboardingProgress]
  );

  const resetConversation = useCallback(() => {
    setState({
      ...initialState,
      sessionId: generateSecureSessionId(), // Generate new sessionId on reset
    });
  }, []);

  return {
    state,
    addMessage,
    setLoading,
    startSignup,
    updateOnboardingProgress,
    extractOnboardingDataFromMessage,
    extractUserDataFromUserMessage,
    resetConversation,
  };
};
