import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';

import { GET_USER_QUERY } from '@/utils/queryKeys';

import {
  USER_PROFILE_QUERY_KEYS,
  useUpdateAvatarMutation,
} from './useUserProfile';

export const useAvatarUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const queryClient = useQueryClient();
  const updateAvatarMutation = useUpdateAvatarMutation();

  // Auto-clear error and success messages
  useEffect(() => {
    if (uploadError) {
      const timer = setTimeout(() => {
        setUploadError(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [uploadError]);

  useEffect(() => {
    if (uploadSuccess) {
      const timer = setTimeout(() => {
        setUploadSuccess(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [uploadSuccess]);

  const createFormData = (file: File): FormData => {
    const formData = new FormData();
    // Process filename to replace spaces with underscores
    const processedFileName = file.name.replace(/\s+/g, '_');
    const processedFile = new File([file], processedFileName, {
      type: file.type,
      lastModified: file.lastModified,
    });
    formData.append('avatar', processedFile);
    return formData;
  };

  const createLocalPreview = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  };

  const validateFile = (file: File): string | null => {
    const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

    if (!allowedTypes.includes(file.type)) {
      return 'Please select a valid image file (JPEG, PNG, or GIF)';
    }

    if (file.size > maxSizeInBytes) {
      return 'File size must be less than 5MB';
    }

    return null;
  };

  const uploadAvatar = useCallback(
    async (file: File): Promise<boolean> => {
      setIsUploading(true);
      setUploadError(null);
      setUploadSuccess(false);
      setUploadProgress(0);

      try {
        // Validate file
        const validationError = validateFile(file);
        if (validationError) {
          setUploadError(validationError);
          return false;
        }

        // Create local preview and set it immediately
        const localPreview = await createLocalPreview(file);
        setPreviewUrl(localPreview);

        // Update query data immediately for instant preview
        // queryClient.setQueryData([GET_USER_QUERY], oldData => {
        //   if (oldData) {
        //     return {
        //       ...oldData,
        //       profilePicture: localPreview,
        //     };
        //   }
        //   return oldData;
        // });

        // Create FormData with file
        const formData = createFormData(file);

        // Upload avatar with real progress tracking
        const response = await updateAvatarMutation.mutateAsync({
          payload: formData,
          onProgress: (progress: number) => {
            setUploadProgress(progress);
          },
        });

        if (!response.status) {
          throw new Error(response.message || 'Avatar upload failed');
        }

        // Show success state
        setUploadSuccess(true);
        setIsUploading(false);
        // Invalidate all user profile related queries
        await Promise.all([
          queryClient.invalidateQueries({ queryKey: [GET_USER_QUERY] }),
          queryClient.invalidateQueries({ queryKey: ['auth'] }),
          queryClient.invalidateQueries({
            queryKey: USER_PROFILE_QUERY_KEYS.all,
          }),
        ]);

        return true;
      } catch (error) {
        // Revert preview on error

        queryClient.invalidateQueries({ queryKey: [GET_USER_QUERY] });
        setPreviewUrl(null);
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to upload avatar';
        setUploadError(errorMessage);
        return false;
      } finally {
        setIsUploading(false);
        setUploadProgress(0);
      }
    },
    [updateAvatarMutation, queryClient]
  );

  const handleFileSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        return uploadAvatar(file);
      }
      return Promise.resolve(false);
    },
    [uploadAvatar]
  );

  return {
    isUploading,
    uploadError,
    uploadProgress,
    uploadSuccess,
    previewUrl,
    uploadAvatar,
    handleFileSelect,
    validateFile,
  };
};
