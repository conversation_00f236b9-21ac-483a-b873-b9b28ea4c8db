import { useQuery } from '@tanstack/react-query';

import { TimezoneData, useGetTimezones } from '../services/timezoneService';

interface TimezoneOption {
  value: string;
  label: string;
}

export const useTimezones = () => {
  const getTimezones = useGetTimezones();

  const {
    data: timezoneResponse,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['timezones'],
    queryFn: getTimezones,
    staleTime: 1000 * 60 * 60 * 24, // 24 hours - timezones don't change often
    cacheTime: 1000 * 60 * 60 * 24 * 7, // 7 days
    retry: 3,
  });

  const timezones: TimezoneData[] = timezoneResponse?.data || [];

  const timezoneOptions: TimezoneOption[] = timezones.map(timezone => ({
    value: timezone.value,
    label: timezone.label,
  }));

  return {
    timezones,
    timezoneOptions,
    isLoading,
    isError,
    error,
    refetch,
  };
};
