import { useQuery } from '@tanstack/react-query';

import { useAssignmentLogService } from '@/services/assignmentLogService';
import type { AssignmentLogFilter } from '@/types/assignmentLog';

export const ASSIGNMENT_LOG_QUERY_KEYS = {
  all: ['assignment-logs'] as const,
  lists: () => [...ASSIGNMENT_LOG_QUERY_KEYS.all, 'list'] as const,
  list: (filter: AssignmentLogFilter) =>
    [...ASSIGNMENT_LOG_QUERY_KEYS.lists(), filter] as const,
  details: () => [...ASSIGNMENT_LOG_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ASSIGNMENT_LOG_QUERY_KEYS.details(), id] as const,
};

export const useAssignmentLogsList = (
  filter: AssignmentLogFilter,
  enabled = true
) => {
  const { getAssignmentLogs } = useAssignmentLogService();
  return useQuery({
    queryKey: ASSIGNMENT_LOG_QUERY_KEYS.list(filter),
    queryFn: () => getAssignmentLogs(filter),
    enabled: enabled,
    select: data => data,
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

export const useAssignmentLogDetails = (id: string, enabled = true) => {
  const { getAssignmentLog } = useAssignmentLogService();
  return useQuery({
    queryKey: ASSIGNMENT_LOG_QUERY_KEYS.detail(id),
    queryFn: () => getAssignmentLog(id),
    enabled: enabled && !!id,
    select: data => data,
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
