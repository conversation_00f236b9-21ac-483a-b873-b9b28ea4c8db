import { ReactNode } from 'react';
import { toast } from 'react-toastify';

export const useCustomToast = () => {
  const successToast = (
    successMessage = '',
    { autoClose = 10_000 }: { autoClose?: number | false | undefined } = {}
  ) => {
    return toast.success(successMessage, {
      position: 'top-right',
      autoClose: autoClose,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  };

  const errorToast = (errorMessage = '') => {
    return toast.error(errorMessage, {
      position: 'top-right',
      autoClose: false,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  };

  const infoToast = (infoMessage: ReactNode = '') => {
    return toast.info(infoMessage, {
      position: 'top-right',
      autoClose: false,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  };

  return {
    successToast,
    errorToast,
    infoToast,
  };
};
