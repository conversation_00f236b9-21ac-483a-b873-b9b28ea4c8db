import { AxiosInstance } from 'axios';

import { publicRequest } from '@/lib/axios/publicRequest';
import {
  ApiResponse,
  ChangePasswordRequest,
  UpdateAvatarPayload,
  UpdateUserInfoRequest,
  UserProfileResponse,
} from '@/types/user';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

class UserProfileService {
  private static instance: UserProfileService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticUserService}/accounts`;
  }

  public static getInstance(): UserProfileService {
    if (!UserProfileService.instance) {
      UserProfileService.instance = new UserProfileService();
    }
    return UserProfileService.instance;
  }

  async updateUserInfo(
    axiosInstance: AxiosInstance,
    payload: UpdateUserInfoRequest
  ): Promise<ApiResponse<UserProfileResponse>> {
    const response = await axiosInstance.patch(
      `${this.BASE_URL}/update-user-info`,
      payload
    );
    return response.data;
  }

  async updateAvatar(
    axiosInstance: AxiosInstance,
    payload: UpdateAvatarPayload,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<string>> {
    const config =
      payload instanceof FormData
        ? {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            onUploadProgress: (progressEvent: any) => {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / (progressEvent.total || 1)
              );
              onProgress?.(percentCompleted);
            },
          }
        : {};

    const response = await axiosInstance.put(
      `${this.BASE_URL}/update-avatar`,
      payload,
      config
    );
    return response.data;
  }

  async changePassword(
    axiosInstance: AxiosInstance,
    payload: ChangePasswordRequest
  ): Promise<ApiResponse<string>> {
    const response = await axiosInstance.patch(
      `${this.BASE_URL}/change-password`,
      payload
    );
    return response.data;
  }

  async changeEmail(
    axiosInstance: AxiosInstance,
    payload: { email: string; token: string }
  ): Promise<ApiResponse<string>> {
    const response = await axiosInstance.patch(
      `${this.BASE_URL}/change-email`,
      payload
    );
    return response.data;
  }

  async verifyEmail(
    axiosInstance: AxiosInstance,
    payload: { token: string }
  ): Promise<ApiResponse<string>> {
    const { token } = payload;
    const response = await axiosInstance.get(
      `${agenticUserService}/users/verify-email?token=${token}`
    );
    return response.data;
  }

  async getUserFunctions(
    axiosInstance: AxiosInstance
  ): Promise<ApiResponse<string[]>> {
    const response = await axiosInstance.get(
      `${agenticUserService}/users/user-functions`
    );
    return response.data;
  }

  async requestEmailVerification(
    axiosInstance: AxiosInstance,
    payload: { email: string }
  ): Promise<ApiResponse<string>> {
    const { email } = payload;
    const response = await axiosInstance.get(
      `${agenticUserService}/request-email-verification?email=${email}`
    );
    return response.data;
  }

  async requestToken(
    axiosInstance: AxiosInstance
  ): Promise<ApiResponse<string>> {
    const response = await axiosInstance.get(`${this.BASE_URL}/request-token`);
    return response.data;
  }

  async validateToken(
    axiosInstance: AxiosInstance,
    token: string
  ): Promise<ApiResponse<string>> {
    const response = await axiosInstance.get(
      `${this.BASE_URL}/validate-token?token=${token}`
    );
    return response.data;
  }

  async requestUnregisteredEmailToken(
    axiosInstance: AxiosInstance,
    email: string
  ): Promise<ApiResponse<string>> {
    const response = await axiosInstance.get(
      `${agenticUserService}/users/request-unregistered-email-token?email=${email}`
    );
    return response.data;
  }

  async verifyUnregisteredEmailToken(
    axiosInstance: AxiosInstance,
    token: string
  ): Promise<ApiResponse<string>> {
    const response = await axiosInstance.get(
      `${agenticUserService}/users/verify-token?token=${token}`
    );
    return response.data;
  }

  async resetPassword(payload: {
    token: string;
    newPassword: string;
  }): Promise<ApiResponse<string>> {
    const response = await publicRequest(BASE_URL).post(
      `${agenticUserService}/users/reset-password`,
      payload
    );
    return response.data;
  }

  async requestPasswordReset(payload: {
    email: string;
  }): Promise<ApiResponse<string>> {
    const { email } = payload;
    const response = await publicRequest(BASE_URL).get(
      `${agenticUserService}/users/request-password-reset?email=${email}`
    );
    return response.data;
  }
}

export default UserProfileService.getInstance();
