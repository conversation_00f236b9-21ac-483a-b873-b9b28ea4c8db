import { usePrivateRequest } from '@/lib/axios/usePrivateRequest';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

export interface TimezoneData {
  value: string;
  label: string;
}

export interface ApiResponse<T> {
  status: boolean;
  message: string;
  data: T;
}

export const useGetTimezones = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getTimezones = async (): Promise<ApiResponse<TimezoneData[]>> => {
    try {
      const res = await axiosInstance.current?.get(
        `${agenticUserService}/timezones`
      );

      if (!res?.data?.status) {
        throw new Error(res?.data?.message || 'Failed to fetch timezones');
      }

      // Transform string array response to TimezoneData array
      const timezones: TimezoneData[] = res.data.data.map(
        (timezone: string) => ({
          value: timezone,
          label: timezone.replace(/_/g, ' '),
        })
      );

      return {
        ...res.data,
        data: timezones,
      };
    } catch (error: any) {
      // Extract meaningful error message from API response
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Failed to fetch timezones';
      throw new Error(errorMessage);
    }
  };

  return getTimezones;
};
