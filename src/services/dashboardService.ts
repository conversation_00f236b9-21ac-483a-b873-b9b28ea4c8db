import { useTenant } from '@/context/TenantContext';
import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import type {
  CreateDailyInsightRequest,
  DailyInsightFilter,
  DailyInsightListResponse,
  DailyInsightResponse,
} from '@/types/dashboard';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

export const useDashboardService = () => {
  const { tenantId, activeAgent } = useTenant();
  const axiosInstance = usePivotlPrivateRequest(
    `${BASE_URL}${agenticService}/daily-insights`,
    tenantId || undefined,
    activeAgent || undefined
  );

  const getDailyInsights = async (
    filter: DailyInsightFilter
  ): Promise<DailyInsightListResponse> => {
    const response = await axiosInstance.current?.get('', {
      params: filter,
    });
    return response?.data;
  };

  const createDailyInsight = async (
    payload: CreateDailyInsightRequest
  ): Promise<DailyInsightResponse> => {
    const response = await axiosInstance.current?.post('', payload);
    return response?.data;
  };

  return {
    getDailyInsights,
    createDailyInsight,
  };
};
