import { useTenant } from '@/context/TenantContext';
import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import type {
  AssignmentLogFilter,
  AssignmentLogListResponse,
  AssignmentLogResponse,
} from '@/types/assignmentLog';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

export const useAssignmentLogService = () => {
  const { tenantId, activeAgent } = useTenant();
  const axiosInstance = usePivotlPrivateRequest(
    `${BASE_URL}${agenticService}/assignment-logs`,
    tenantId || undefined,
    activeAgent || undefined
  );

  const getAssignmentLogs = async (
    filter: AssignmentLogFilter
  ): Promise<AssignmentLogListResponse> => {
    const { search, ...rest } = filter;
    const response = await axiosInstance.current?.get('', {
      params: {
        search,
        ...rest,
      },
    });
    return response?.data;
  };

  const getAssignmentLog = async (
    id: string
  ): Promise<AssignmentLogResponse> => {
    const response = await axiosInstance.current?.get(`/${id}`);
    return response?.data;
  };

  return {
    getAssignmentLogs,
    getAssignmentLog,
  };
};
