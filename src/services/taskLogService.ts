import { useTenant } from '@/context/TenantContext';
import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import type { TaskLogFilter } from '@/types/taskLog';
import {
  CreateTaskLogRequest,
  DeleteTaskLogResponse,
  TaskLogListResponse,
  TaskLogResponse,
  UpdateTaskLogRequest,
} from '@/types/taskLog';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

export const useTaskLogService = () => {
  const { tenantId, activeAgent } = useTenant();
  const axiosInstance = usePivotlPrivateRequest(
    `${BASE_URL}${agenticService}/task-logs`,
    tenantId || undefined,
    activeAgent || undefined
  );

  const createTaskLog = async (
    payload: CreateTaskLogRequest
  ): Promise<TaskLogResponse> => {
    const response = await axiosInstance.current?.post('', payload);
    return response?.data;
  };

  const getTaskLog = async (id: string): Promise<TaskLogResponse> => {
    const response = await axiosInstance.current?.get(`/${id}`);
    return response?.data;
  };

  const getTaskLogs = async (
    filter: TaskLogFilter
  ): Promise<TaskLogListResponse> => {
    const { search, ...rest } = filter;
    const response = await axiosInstance.current?.get('', {
      params: {
        search,
        ...rest,
      },
    });
    return response?.data;
  };

  const getTaskLogsByTenant = async (
    tenantId: string
  ): Promise<TaskLogListResponse> => {
    const response = await axiosInstance.current?.get(`/tenant/${tenantId}`);
    return response?.data;
  };

  const updateTaskLog = async (
    id: string,
    payload: UpdateTaskLogRequest
  ): Promise<TaskLogResponse> => {
    const response = await axiosInstance.current?.put(`/${id}`, payload);
    return response?.data;
  };

  const deleteTaskLog = async (id: string): Promise<DeleteTaskLogResponse> => {
    const response = await axiosInstance.current?.delete(`/${id}`);
    return response?.data;
  };

  return {
    createTaskLog,
    getTaskLog,
    getTaskLogs,
    getTaskLogsByTenant,
    updateTaskLog,
    deleteTaskLog,
  };
};
