import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import { TwilioConnectionModal } from '@/components/businessStack/TwilioConnectionModal';
import AgentsDropdown from '@/components/ui/AgentsDropdown';
import { useTenant } from '@/context/TenantContext';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { UserBasicInfoPayload } from '@/types/user';
import {
  formatAppNameFromKey,
  hasOAuthError,
} from '@/utils/oauthRedirectHandler';

import { Icons } from '../../assets/icons/DashboardIcons';
import { AvailableAppsGrid } from '../../components/businessStack/AvailableAppsGrid';
import OAuthErrorModal from '../../components/businessStack/OAuthErrorModal';
import AppContainer from '../../components/common/AppContainer';
import EnhancedChatSidebar from '../../components/common/EnhancedChatSidebar';
import { useConnectionFlow } from '../../hooks/useConnectionFlow';
import { useAvailableAppsApi } from '../../services/businessStackService';
import { BusinessStackPageState } from '../../types/businessStack';

const BusinessStackPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams<{ appKey?: string }>();
  const [pageState, setPageState] = useState<BusinessStackPageState>({
    availableApps: [],
    isLoadingApps: true,
    searchQuery: '',
    selectedCategory: '',
    scyraChatState: {
      messages: [],
      isLoading: false,
      sessionId: '',
    },
    currentPage: 1,
    totalPages: 1,
  });

  const [selectedSuite, setSelectedSuite] = useState<string>('');
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] = useState(false);
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] = useState(false);
  const suiteDropdownRef = useRef<HTMLDivElement>(null);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(suiteDropdownRef, () => setIsSuiteDropdownOpen(false));
  useOnClickOutside(agentDropdownRef, () => setIsAgentDropdownOpen(false));

  const [isProcessingOAuth, setIsProcessingOAuth] = useState(false);
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const oauthCallbackProcessedRef = useRef<string | null>(null); // Track processed OAuth callbacks

  // Initialize connection flow
  const connectionFlow = useConnectionFlow((app, isConnected) => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(existingApp =>
        existingApp.key === app.key
          ? { ...existingApp, isConnected }
          : existingApp
      ),
    }));
  });

  const [twilioModal, setTwilioModal] = useState({
    isOpen: false,
  });

  const [oauthErrorModal, setOAuthErrorModal] = useState({
    isOpen: false,
    appName: '',
  });

  const getAvailableApps = useAvailableAppsApi();

  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const {
    activeAgent,
    setActiveAgent,
    isLoading: isActiveTenantLoading,
  } = useTenant();

  // Get suite options from user data
  const suiteOptions =
    userData?.userInfo?.tenant?.claimedAgentSuites?.map(suite => ({
      id: suite.suite.agentSuiteKey,
      name: suite.suite.agentSuiteName,
      icon: suite.suite.avatar,
      fileData: suite.suite.fileData,
    })) || [];

  // Get all agents from all suites
  const allAgents =
    userData?.userInfo?.tenant?.claimedAgentSuites?.flatMap(suite =>
      suite.suite.availableAgents.map(agent => ({
        ...agent,
        suiteKey: suite.suite.agentSuiteKey,
      }))
    ) || [];

  // Initialize selections
  useEffect(() => {
    if (suiteOptions.length > 0 && !selectedSuite) {
      setSelectedSuite(suiteOptions[0].id);
    }
    if (allAgents.length > 0 && !selectedAgent) {
      setSelectedAgent(activeAgent || allAgents[0].agentKey);
    }
  }, [suiteOptions, allAgents, selectedSuite, selectedAgent]);

  const handleAgentChange = (agentKey: string) => {
    setSelectedAgent(agentKey);
    setActiveAgent(agentKey);
    setIsAgentDropdownOpen(false);
  };

  // Trigger reload of available apps and reset connection flow when agent changes
  useEffect(() => {
    if (isActiveTenantLoading || !activeAgent) return;

    const reloadForAgent = async () => {
      try {
        setPageState(prev => ({ ...prev, isLoadingApps: true }));

        // Reload chat history for the new agent
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }

        // Use current page state values for consistency
        const response = await getAvailableApps({
          page: pageState.currentPage,
          size: 10,
          search: pageState.searchQuery,
          appCategory: pageState.selectedCategory,
        });

        setPageState(prev => ({
          ...prev,
          availableApps: response.data.availableApps,
          isLoadingApps: false,
          totalPages: response.data.total,
        }));

        // Reset connection flow state completely (don't preserve messages from other agents)
        connectionFlow.resetFlow(false);
      } catch (error) {
        console.error('Error reloading apps for agent change:', error);
        setPageState(prev => ({ ...prev, isLoadingApps: false }));
      }
    };

    reloadForAgent();
  }, [activeAgent, isActiveTenantLoading]);

  // Handle OAuth callback parameters on component mount
  useEffect(() => {
    const handleOAuthCallback = async () => {
      const urlParams = new URLSearchParams(location.search);
      const appKey = params.appKey; // Get appKey from path parameter
      const code = urlParams.get('code');
      const state = urlParams.get('state');

      // Check for OAuth errors first
      if (appKey && hasOAuthError()) {
        const appName = formatAppNameFromKey(appKey);
        setOAuthErrorModal({
          isOpen: true,
          appName,
        });

        // Clean up URL - navigate back to business-stack without appKey
        navigate('/dashboard/business-stack', { replace: true });
        return;
      }

      // Only process if we have both appKey and code (OAuth callback scenario)
      if (appKey && code) {
        // Create unique callback identifier to prevent duplicate processing
        const callbackId = `${appKey}-${code}-${state}`;

        // Check if this callback has already been processed
        if (oauthCallbackProcessedRef.current === callbackId) {
          // console.log(
          //   'OAuth callback already processed, skipping:',
          //   callbackId
          // );
          return;
        }

        // Check if already processing OAuth to prevent concurrent processing
        if (isProcessingOAuth) {
          // console.log('OAuth callback already in progress, skipping');
          return;
        }

        // console.log('Processing OAuth callback for:', callbackId);
        oauthCallbackProcessedRef.current = callbackId;
        setIsProcessingOAuth(true);

        // Small delay to ensure all state updates are complete
        await new Promise(resolve => setTimeout(resolve, 100));

        try {
          // Find the app by key to set it as current app for the connection flow
          const response = await getAvailableApps({
            page: 1,
            size: 100, // Get all apps to find the one we need
            search: '',
            appCategory: '',
          });

          const targetApp = response.data.availableApps.find(
            app => app.key === appKey
          );

          if (targetApp) {
            // console.log('Found target app for OAuth callback:', targetApp.name);
            // Process the OAuth callback with the full app object
            // DO NOT call startConnection here as it would trigger another OAuth flow
            await connectionFlow.handleOAuthCallbackWithApp(
              targetApp,
              code,
              state || undefined
            );
            // console.log(
            //   'OAuth callback processing completed for:',
            //   targetApp.name
            // );
          } else {
            console.error('App not found for key:', appKey);
            throw new Error(`App not found for key: ${appKey}`);
          }
        } catch (error) {
          console.error('Error processing OAuth callback:', error);
          // Reset the processed flag on error so it can be retried
          oauthCallbackProcessedRef.current = null;
        } finally {
          setIsProcessingOAuth(false);

          // Clean up URL - navigate back to business-stack without appKey
          navigate('/dashboard/business-stack', { replace: true });
        }
      } // Close the if (appKey && code) block
    };

    handleOAuthCallback();
  }, [location.search, params.appKey, isProcessingOAuth]); // Include isProcessingOAuth to prevent concurrent processing

  // Load available apps on component mount
  useEffect(() => {
    const loadAvailableApps = async () => {
      try {
        setPageState(prev => ({ ...prev, isLoadingApps: true }));
        const response = await getAvailableApps({
          page: pageState.currentPage,
          size: 10,
          search: pageState.searchQuery,
          appCategory: pageState.selectedCategory,
        });

        setPageState(prev => ({
          ...prev,
          availableApps: response.data.availableApps,
          isLoadingApps: false,
          totalPages: response.data.total,
        }));
      } catch (error) {
        console.error('Error loading available apps:', error);
        setPageState(prev => ({ ...prev, isLoadingApps: false }));
      }
    };

    loadAvailableApps();
  }, [
    pageState.currentPage,
    pageState.searchQuery,
    pageState.selectedCategory,
  ]);

  // Handle search query changes
  const handleSearchChange = (query: string) => {
    setPageState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));
  };

  // Handle category changes
  const handleCategoryChange = (category: string) => {
    setPageState(prev => ({
      ...prev,
      selectedCategory: category,
      currentPage: 1, // Reset to first page when filtering
    }));
  };

  const handleOpenTwilioModal = () => {
    setTwilioModal({
      isOpen: true,
    });
  };

  const handleCloseTwilioModal = () => {
    setTwilioModal({
      isOpen: false,
    });
  };

  const handleTwilioConnectionSuccess = () => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === 'Twilio' ? { ...app, isConnected: true } : app
      ),
    }));
  };

  const handleCloseOAuthErrorModal = () => {
    setOAuthErrorModal({
      isOpen: false,
      appName: '',
    });
  };

  // Handle app connection (for non-modal apps)
  const handleConnectApp = async (appName: string) => {
    // This will be handled by the connection flow now
    console.log('Connecting to app:', appName);
  };

  // Show loading state while processing OAuth callback
  if (isProcessingOAuth) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex flex-1 items-center justify-center">
          <div className="flex flex-col items-center justify-center">
            <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="text-blackFour">Processing authentication...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <div className="flex flex-1 overflow-hidden">
        <div className="flex flex-1 overflow-hidden">
          {/* LHS - Chat Interface */}
          <EnhancedChatSidebar
            connectionFlow={connectionFlow}
            reloadChatHistoryRef={reloadChatHistoryRef}
          />

          {/* RHS - Available Apps Grid */}
          <div className="flex-1 overflow-y-auto">
            <AppContainer className="space-y-6 p-8 lg:space-y-8">
              <div className="lg:max-w-[800px]">
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center text-primary">
                        <Icons.Stack className="h-6 w-6" />
                      </div>
                      <h1 className="text-xl font-semibold text-blackFour lg:text-2xl">
                        Business Stack
                      </h1>
                    </div>
                  </div>

                  {/* AgentsDropdown Component */}
                  <div className="mt-6 flex items-center justify-between">
                    <div className="relative" ref={suiteDropdownRef}>
                      <AgentsDropdown
                        isOpen={isSuiteDropdownOpen}
                        onToggle={() =>
                          setIsSuiteDropdownOpen(!isSuiteDropdownOpen)
                        }
                        currentItem={suiteOptions.find(
                          s => s.id === selectedSuite
                        )}
                        options={suiteOptions}
                        onItemSelect={suite => {
                          setSelectedSuite(suite.id);
                          setIsSuiteDropdownOpen(false);
                        }}
                        placeholder="Suite"
                        noOptionsMessage="No other suites available"
                      />
                    </div>

                    <div className="relative" ref={agentDropdownRef}>
                      <AgentsDropdown
                        isOpen={isAgentDropdownOpen}
                        onToggle={() =>
                          setIsAgentDropdownOpen(!isAgentDropdownOpen)
                        }
                        currentItem={allAgents
                          .map(a => ({
                            id: a.agentKey,
                            name: a.agentName,
                            icon: a.avatar,
                          }))
                          .find(a => a.id === selectedAgent)}
                        options={allAgents.map(a => ({
                          id: a.agentKey,
                          name: a.agentName,
                          icon: a.avatar,
                        }))}
                        onItemSelect={agent => handleAgentChange(agent.id)}
                        placeholder="Agent"
                        noOptionsMessage="No other agents available"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex-1 overflow-hidden">
                  <AvailableAppsGrid
                    apps={pageState.availableApps}
                    isLoading={pageState.isLoadingApps}
                    searchQuery={pageState.searchQuery}
                    selectedCategory={pageState.selectedCategory}
                    onSearchChange={handleSearchChange}
                    onCategoryChange={handleCategoryChange}
                    onConnectApp={handleConnectApp}
                    connectionFlow={connectionFlow}
                    onOpenTwilioModal={handleOpenTwilioModal}
                  />
                </div>
              </div>
            </AppContainer>
          </div>
        </div>
        <TwilioConnectionModal
          isOpen={twilioModal.isOpen}
          onClose={handleCloseTwilioModal}
          onConnectionSuccess={handleTwilioConnectionSuccess}
        />
        <OAuthErrorModal
          isOpen={oauthErrorModal.isOpen}
          onClose={handleCloseOAuthErrorModal}
          appName={oauthErrorModal.appName}
        />
      </div>
    </div>
  );
};

export default BusinessStackPage;
