import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import { Link } from 'react-router-dom';

import { AgentousLogo } from '@/assets/images';
import bgImage from '@/assets/images/onboardingBgImage.png';

export default function NewPasswordContainer({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <motion.div
      exit={{ opacity: 0, transition: { duration: 0.2 } }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      style={{ backgroundImage: `url(${bgImage})` }}
      className="relative mx-auto flex h-screen max-w-full justify-center overflow-hidden bg-cover"
    >
      <Link className="absolute left-[7%] top-[5%]" to="/">
        <div className="min-w-[80px] max-w-[117px]">
          <img loading="lazy" src={AgentousLogo} className={`h-full w-full`} />
        </div>
      </Link>
      {children}
    </motion.div>
  );
}
