import { yup<PERSON>esolver } from '@hookform/resolvers/yup';
import { useKeycloak } from '@react-keycloak/web';
import { AxiosError } from 'axios';
import { Loader2 } from 'lucide-react';
import { ReactNode, useEffect, useState } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import * as yup from 'yup';

import { Icons } from '@/assets/icons/DashboardIcons';
import { SuccessIcon } from '@/assets/images';
import { Button, Input } from '@/components/ui';
import { useResetPassword } from '@/hooks/useAuth';
import { IPasswordPayload } from '@/types/user';
import { passwordRegex } from '@/utils/helpers';

const passwordSchema = yup
  .object()
  .shape({
    password: yup
      .string()
      .required('Password is a required field')
      .matches(
        passwordRegex,
        'Your password needs to be at least 8 characters, One Uppercase, One Number and One Special Character.'
      ),
    confirmPassword: yup
      .string()
      .required('Confirm your password.')
      .test('passwords-match', 'Passwords must match', function (value) {
        return this.parent.password === value;
      }),
  })
  .required();

export default function NewPasswordForm() {
  const { keycloak } = useKeycloak();
  const { token } = useParams();
  const [password, setPassword] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const next = () => {
    setShowSuccess(true);
  };

  const { isPending: isLoading, mutateAsync, isSuccess } = useResetPassword();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<IPasswordPayload>({
    resolver: yupResolver(passwordSchema),
  });

  useEffect(() => {
    if (isSuccess) {
      next();
      reset();
    }
  }, [isSuccess]);

  // Clear error message after 3 seconds
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => {
        setErrorMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  const onSubmit: SubmitHandler<IPasswordPayload> = async ({
    password,
    confirmPassword,
  }) => {
    const newPayload = {
      token: token as string,
      password: password.trim(),
      confirmPassword,
    };
    setErrorMessage(''); // Clear any existing errors

    try {
      await mutateAsync(newPayload);
    } catch (error) {
      // Extract error message from axios error response
      const axiosError = error as AxiosError<{ message?: string }>;
      const message =
        axiosError.response?.data?.message ||
        'An error occurred while resetting your password';
      setErrorMessage(message);
    }
  };
  return (
    <div className="flex flex-col md:flex-row">
      <div className="mx-auto mt-8 h-full w-full max-w-[400px] px-5 py-8 sm:py-20">
        {showSuccess ? (
          <div className="flex flex-col items-center pt-12 sm:pt-24">
            <SuccessIcon className="mb-8" />
            <p className="text-xl font-semibold">Password reset successfully</p>
            <Button
              onClick={() => {
                keycloak.login({
                  redirectUri: `${window.location.origin}/dashboard`,
                });
              }}
              type="button"
              className="mt-5 w-full max-w-[318px] text-white"
            >
              Login{' '}
            </Button>
          </div>
        ) : (
          <>
            <h2 className="mb-1 pt-12 text-center text-[20px] font-[500] leading-[47px] sm:pt-24 sm:text-[32px]">
              Create Password
            </h2>
            <p className="text-center text-[16px] leading-[27px]">
              Create a password for your Agentous account with the details you
              have provided
            </p>
            <form onSubmit={handleSubmit(onSubmit)} className="mt-7 w-full">
              <div className="">
                <div className="">
                  <label
                    htmlFor="email"
                    className="mb-1 block text-sm font-medium text-subText"
                  >
                    Create Password
                  </label>
                  <Input
                    className="h-10 w-full"
                    placeholder="Password"
                    autoComplete="password"
                    id="password"
                    {...register('password')}
                    type={password ? 'text' : 'password'}
                    endIcon={
                      password ? (
                        <button
                          type="button"
                          onClick={() => setPassword(previous => !previous)}
                        >
                          <Icons.PasswordView className="h-5 w-5" />
                        </button>
                      ) : (
                        <button
                          type="button"
                          onClick={() => setPassword(previous => !previous)}
                        >
                          <Icons.PasswordHide className="h-5 w-5" />
                        </button>
                      )
                    }
                  />
                </div>
                <div className="mt-5">
                  <label
                    htmlFor="email"
                    className="mb-1 block text-sm font-medium text-subText"
                  >
                    Confirm Password
                  </label>
                  <Input
                    className="h-10 w-full"
                    placeholder="Confirm Password"
                    autoComplete="confirm-password"
                    id="confirmPassword"
                    {...register('confirmPassword')}
                    type={confirmPassword ? 'text' : 'password'}
                    endIcon={
                      confirmPassword ? (
                        <button
                          type="button"
                          onClick={() =>
                            setConfirmPassword(previous => !previous)
                          }
                        >
                          <Icons.PasswordView className="h-5 w-5" />
                        </button>
                      ) : (
                        <button
                          type="button"
                          onClick={() =>
                            setConfirmPassword(previous => !previous)
                          }
                        >
                          <Icons.PasswordHide className="h-5 w-5" />
                        </button>
                      )
                    }
                  />
                </div>
                {(errors?.confirmPassword ||
                  errors?.password ||
                  errorMessage) && (
                  <p className="mt-2 pr-8 text-[11px] text-red-800 sm:text-[14px]">
                    {errorMessage ||
                      (errors.confirmPassword?.message as ReactNode) ||
                      (errors.password?.message as ReactNode)}
                  </p>
                )}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="mx-auto mt-6 w-full border border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <p className={`text-sm font-medium sm:text-base`}>Next</p>
                  )}
                </Button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
}
