import clsx from 'clsx';
import React from 'react';

import {
  AssignmentLogDetailsFormData,
  AssignmentLogStatusDisplay,
} from '@/types/assignmentLog';

import CustomDropdown, {
  DropdownOption,
} from '../../TaskLogsPage/components/CustomDropdown';

interface AssignmentLogStatusCheckInsProps {
  log: AssignmentLogDetailsFormData;
  editForm: Partial<AssignmentLogDetailsFormData>;
  isEditing: boolean;
  onSelectChange: (
    field: keyof AssignmentLogDetailsFormData,
    value: string
  ) => void;
}

const AssignmentLogStatusCheckIns: React.FC<
  AssignmentLogStatusCheckInsProps
> = ({ log, editForm, isEditing, onSelectChange }) => {
  const statusOptionStrings: AssignmentLogStatusDisplay[] = [
    'Pending',
    'In Progress',
    'Completed',
  ];

  const getCheckInsColor = (checkIns: string) => {
    const colorMap = {
      Hourly: 'bg-[#FBA320] text-white',
      Daily: 'bg-[#FBA320] text-white',
      Weekly: 'bg-[#FBA320] text-white',
      Quarterly: 'bg-[#FBA320] text-white',
      Monthly: 'bg-[#FBA320] text-white',
    };
    return (
      colorMap[checkIns as keyof typeof colorMap] || 'bg-gray-500 text-white'
    );
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      Completed: 'bg-blackOne text-white',
      'In Progress': 'bg-purpleOne text-white',
      Pending: 'bg-grayTen text-white',
    };
    return (
      colorMap[status as keyof typeof colorMap] || 'bg-gray-500 text-white'
    );
  };

  const statusOptions: DropdownOption[] = statusOptionStrings.map(s => ({
    value: s,
    label: s,
  }));

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <div className="rounded-none border border-[#DFEAF2] p-4">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Status</h3>
        {isEditing ? (
          <CustomDropdown
            options={statusOptions}
            value={editForm.status || log.status}
            onChange={value => onSelectChange('status', value)}
            buttonClassName={clsx(
              'w-fit min-w-[136px] h-[44px]',
              getStatusColor(editForm.status || log.status)
            )}
            renderButtonContent={selectedOption => (
              <span className="flex-grow text-left">
                {selectedOption?.label}
              </span>
            )}
            dropdownClassName="w-[150px]!"
          />
        ) : (
          <span
            className={clsx(
              'inline-flex h-[44px] items-center gap-2 rounded-full px-6 text-sm font-medium',
              getStatusColor(log.status)
            )}
          >
            {log.status}
          </span>
        )}
      </div>

      <div className="hidden rounded-none border border-[#DFEAF2] p-4">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Check Ins</h3>

        {log?.checkIns ? (
          <span
            className={clsx(
              'inline-flex h-[44px] items-center gap-2 rounded-full px-6 text-sm font-medium',
              getCheckInsColor(log.checkIns || '')
            )}
          >
            {log.checkIns}
          </span>
        ) : (
          <span className="inline-flex h-[44px] items-center gap-2 rounded-full px-6 text-sm font-medium">
            --
          </span>
        )}
      </div>
    </div>
  );
};

export default AssignmentLogStatusCheckIns;
