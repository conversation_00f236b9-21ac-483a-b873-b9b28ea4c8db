import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { useAssignmentLogDetails } from '@/hooks/useAssignmentLog';
import {
  AssignmentLogDetailsFormData,
  AssignmentLogResponse,
  mapAssignmentStatusToDisplay,
} from '@/types/assignmentLog';

import {
  AlertBanner,
  LogDetailsError,
  LogDetailsHeader,
  LogDetailsSkeleton,
  SuccessModal,
} from '../TaskLogsPage/components';
import AssignmentLogAdditionalFields from './components/AssignmentLogAdditionalFields';
import AssignmentLogBasicInfo from './components/AssignmentLogBasicInfo';
import AssignmentLogStatusCheckIns from './components/AssignmentLogStatusCheckIns';

// Helper function to format dates to YYYY-MM-DD
const formatDateForInput = (dateString: string | Date): string => {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  } catch (error) {
    return '';
  }
};

const AssignmentLogDetailsPage: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const assignmentLogId = id;
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editForm, setEditForm] = useState<
    Partial<AssignmentLogDetailsFormData>
  >({});
  const [showSuccessModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // Use the assignment log hook to fetch data
  const {
    data: assignmentLog,
    isLoading,
    error,
    refetch,
  } = useAssignmentLogDetails(assignmentLogId || '', !!assignmentLogId);

  // Convert AssignmentLog to AssignmentLogDetailsFormData for UI
  const convertToFormData = (
    data: AssignmentLogResponse | undefined
  ): AssignmentLogDetailsFormData => {
    if (!data) return {} as AssignmentLogDetailsFormData;

    return {
      assignmentTitle: '',
      description: data?.data?.description || 'No description provided',
      time: data?.data?.createdAt
        ? new Date(data?.data?.createdAt).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          })
        : '--',
      dateCreated: data?.data?.createdAt
        ? new Date(data?.data?.createdAt).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })
        : '--',
      dueDate: data?.data?.dueDate
        ? formatDateForInput(data?.data?.dueDate)
        : '',
      createdBy: data.data.assignedTo || 'Unassigned',
      type: '--',
      checkIns: null, // Default check-ins frequency
      status: mapAssignmentStatusToDisplay(data?.data?.status),
      escalationPolicy: '--',
    };
  };

  const formData = convertToFormData(assignmentLog);

  // Utility function to extract user-friendly error message
  const extractErrorMessage = (error: any): string => {
    if (error?.response?.data?.message) return error.response.data.message;
    if (error?.response?.data?.data?.message)
      return error.response.data.data.message;
    if (
      error?.message &&
      !error.message.includes('403') &&
      !error.message.includes('404') &&
      !error.message.includes('500')
    ) {
      return error.message;
    }
    if (error?.response?.status === 403)
      return "You don't have permission to perform this action";
    if (error?.response?.status === 404) return 'Assignment log not found';
    if (error?.response?.status === 500)
      return 'Something went wrong on our end. Please try again';
    return 'An unexpected error occurred. Please try again';
  };

  // Utility functions for handling messages
  const showError = (message: string) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(''), 3000);
  };

  const showSuccess = (message: string) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const handleBack = () => {
    navigate(-1);
  };

  // Check if form has changes compared to original data
  const checkForChanges = (
    newFormData: Partial<AssignmentLogDetailsFormData>
  ) => {
    const hasFormChanges =
      newFormData.description !== formData.description ||
      newFormData.status !== formData.status ||
      newFormData.checkIns !== formData.checkIns ||
      newFormData.dueDate !== formData.dueDate ||
      newFormData.escalationPolicy !== formData.escalationPolicy;

    setHasChanges(hasFormChanges);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditForm(formData);
    setHasChanges(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditForm(formData);
    setHasChanges(false);
  };

  const handleSave = async () => {
    if (!assignmentLog || !assignmentLogId) return;

    try {
      // Mock save - implement actual update mutation here
      setIsEditing(false);
      setHasChanges(false);
      showSuccess('Assignment log updated successfully!');
    } catch (error) {
      const errorMsg = extractErrorMessage(error);
      showError(errorMsg);
    }
  };

  const handleInputChange = (
    field: keyof AssignmentLogDetailsFormData,
    value: string
  ) => {
    const newFormData = { ...editForm, [field]: value };
    setEditForm(newFormData);
    checkForChanges(newFormData);
  };

  const handleSelectChange = (
    field: keyof AssignmentLogDetailsFormData,
    value: string
  ) => {
    const newFormData = { ...editForm, [field]: value };
    setEditForm(newFormData);
    checkForChanges(newFormData);
  };

  // Render main content based on state
  const renderMainContent = () => {
    if (isLoading && !assignmentLog) {
      return <LogDetailsSkeleton />;
    }

    if (error && !assignmentLog) {
      return (
        <LogDetailsError
          message={error instanceof Error ? error.message : 'An error occurred'}
          onRetry={() => refetch()}
        />
      );
    }

    if (!assignmentLog) {
      return (
        <LogDetailsError
          message="Assignment log not found"
          onBack={handleBack}
        />
      );
    }

    return (
      <div className="flex flex-1 flex-col gap-8 overflow-y-auto px-8 pb-20">
        {/* Header */}
        <LogDetailsHeader
          taskTitle={'Assignment Log'}
          isEditing={isEditing}
          saving={false}
          hasChanges={hasChanges}
          onBack={handleBack}
          onEdit={handleEdit}
          onSave={handleSave}
          onCancel={handleCancelEdit}
        />
        <div className="max-w-full space-y-8">
          {/* Basic Information */}
          <AssignmentLogBasicInfo
            log={formData}
            editForm={editForm}
            isEditing={isEditing}
            onInputChange={handleInputChange}
          />

          {/* Status and Check Ins */}
          <AssignmentLogStatusCheckIns
            log={formData}
            editForm={editForm}
            isEditing={isEditing}
            onSelectChange={handleSelectChange}
          />

          {/* Additional Fields */}
          <AssignmentLogAdditionalFields
            log={formData}
            editForm={editForm}
            isEditing={isEditing}
            onInputChange={handleInputChange}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="relative flex h-full flex-1 flex-col">
      {/* Error Banner */}
      {errorMessage && (
        <AlertBanner
          message={errorMessage}
          type="error"
          onClose={() => setErrorMessage('')}
        />
      )}

      {/* Success Banner */}
      {successMessage && (
        <AlertBanner
          message={successMessage}
          type="success"
          onClose={() => setSuccessMessage('')}
        />
      )}

      {renderMainContent()}

      <SuccessModal isOpen={showSuccessModal} />
    </div>
  );
};

export default AssignmentLogDetailsPage;
