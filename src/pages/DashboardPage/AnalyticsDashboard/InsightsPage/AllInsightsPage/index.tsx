import { ChevronLeft, Filter, Search } from 'lucide-react';
import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import Pagination from '@/components/common/Pagination';
import { Button } from '@/components/ui/Button';
import { useTenant } from '@/context/TenantContext';
import { useDailyInsightsList } from '@/hooks/useDashboard';
import { useAnalyticsParams } from '@/utils/urlParams';

interface InsightData {
  id: string;
  insight: string;
  type: 'warning' | 'info' | 'suggestion';
  timestamp: string;
}

const InsightItem: React.FC<InsightData> = ({ insight, timestamp }) => {
  return (
    <div className="animate-fadeIn flex items-center justify-between rounded-lg bg-white p-4 transition-all duration-150 hover:border-primary">
      <div className="flex w-full items-center">
        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
          <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
        </div>
        <div className="flex-1">
          <span className="mb-1 block text-xs font-normal text-subText sm:text-sm">
            {insight}
          </span>
          <span className="hidden text-xs text-gray-500">{timestamp}</span>
        </div>
      </div>
    </div>
  );
};

const AllInsightsPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { filters } = useAnalyticsParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { tenantId } = useTenant();
  const pageSize = 10;

  // Get filters from URL params
  const searchParams = new URLSearchParams(location.search);
  const fromDate = searchParams.get('from') || undefined;
  const toDate = searchParams.get('to') || undefined;

  // Build filter for insights - include agent filter by default
  const insightFilter = {
    tenantId: tenantId || '',
    createdBy: filters.agent || '',
    search: searchQuery,
    from: fromDate,
    to: toDate,
    page: currentPage - 1, // Convert to 0-based indexing
    pageSize: pageSize,
  };

  // Fetch insights using the hook
  const {
    data: insightsResponse,
    isLoading,
    isError,
  } = useDailyInsightsList(insightFilter, !!tenantId);

  const insights = insightsResponse?.data?.insights || [];
  const totalCount = insightsResponse?.data?.total || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // The API already handles search filtering, so we don't need to filter again here
  const filteredInsights = insights;

  return (
    <div className="flex h-full flex-col gap-6 p-6">
      {/* Header */}
      <div className="relative flex items-center justify-between gap-4">
        <div className="flex items-start gap-2">
          <button onClick={() => navigate(-1)} className="mt-1">
            <ChevronLeft className="h-5 w-5" strokeWidth={3} />
          </button>
          <div className="flex flex-col items-start gap-1 text-blackTwo">
            <h1 className="text-lg font-semibold">
              Daily Insight Commentary (from {filters.agent})
            </h1>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-[#979797]" />
            <input
              type="text"
              placeholder="Search insights..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
            />
          </div>
          <Button className="hidden h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white">
            <span>Filter</span>
            <Filter className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Insights Content */}
      <div className="flex flex-1 flex-col gap-4">
        {isLoading ? (
          <div className="space-y-4 py-6">
            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                className="flex animate-pulse items-center space-x-4"
              >
                <div className="h-12 w-12 rounded-2xl bg-gray-200"></div>
                <div className="h-5 w-3/4 rounded-full bg-gray-200"></div>
              </div>
            ))}
          </div>
        ) : isError ? (
          <div className="flex flex-col items-center justify-center py-12">
            <p className="mb-2 text-lg text-red-600">Error loading insights</p>
            <p className="text-sm text-gray-500">Please try again later</p>
          </div>
        ) : filteredInsights.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <p className="mb-2 text-lg text-gray-600">No insights found</p>
            <p className="text-sm text-gray-500">
              Try adjusting your search criteria or filters
            </p>
          </div>
        ) : (
          filteredInsights.map(insight => (
            <InsightItem
              key={insight.id}
              id={insight.id}
              insight={insight.observation}
              type={'info'}
              timestamp={new Date(insight.createdAt).toLocaleString()}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      {!isLoading && totalPages > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={undefined}
          className="mt-auto"
          downloadButtonText="Download Report"
        />
      )}
    </div>
  );
};

export default AllInsightsPage;
