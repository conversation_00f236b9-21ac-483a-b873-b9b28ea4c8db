import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import { DailyInsight } from '@/types/dashboard';

interface InsightData {
  id: string;
  insight: string;
  type: 'warning' | 'info' | 'suggestion';
}

const InsightItem: React.FC<InsightData> = ({ insight }) => {
  return (
    <div
      className={`animate-fadeIn flex items-center justify-between rounded-lg bg-white py-4 transition-all duration-150 hover:border-primary`}
    >
      <div className="flex items-center">
        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
          <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
        </div>
        <span className="text-xs font-normal text-subText sm:text-sm">
          {insight}
        </span>
      </div>
    </div>
  );
};

interface DailyInsightCommentaryProps {
  insights?: DailyInsight[];
  isLoading?: boolean;
  isError?: boolean;
}

const DailyInsightCommentary: React.FC<DailyInsightCommentaryProps> = ({
  insights,
  isLoading,
  isError,
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="flex animate-pulse items-center space-x-4"
          >
            <div className="h-12 w-12 rounded-2xl bg-gray-200"></div>
            <div className="h-5 w-3/4 rounded-full bg-gray-200"></div>
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-500">
          Failed to load insights. Please try again.
        </p>
      </div>
    );
  }

  if (!insights || insights?.length === 0) {
    return (
      <div className="py-8 text-center">
        <p className="text-subText">No insights available for today.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col divide-y divide-gray-100">
      {insights?.map(insight => (
        <InsightItem
          key={insight.id}
          id={insight.id}
          insight={insight.observation}
          type={'info'}
        />
      ))}
    </div>
  );
};

export default DailyInsightCommentary;
