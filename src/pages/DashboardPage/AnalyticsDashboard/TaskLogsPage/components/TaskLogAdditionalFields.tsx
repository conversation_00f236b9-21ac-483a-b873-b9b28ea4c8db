import clsx from 'clsx';
import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import { TaskLogDetailsFormData } from '@/types/taskLog';

interface TaskLogAdditionalFieldsProps {
  taskLog: TaskLogDetailsFormData;
  editForm: Partial<TaskLogDetailsFormData>;
  isEditing: boolean;
  onInputChange: (field: keyof TaskLogDetailsFormData, value: string) => void;
}

const TaskLogAdditionalFields: React.FC<TaskLogAdditionalFieldsProps> = ({
  taskLog,
  editForm,
  isEditing,
  onInputChange,
}) => {
  const fieldConfig = [
    {
      key: 'assignedTo' as keyof TaskLogDetailsFormData,
      label: 'Assigned To',
      icon: Icons.AssignedByIcon,
      value: taskLog.createdBy,
      editValue: editForm.createdBy,
      placeholder: 'Enter assignee name',
    },
    {
      key: 'type' as keyof TaskLogDetailsFormData,
      label: 'Type',
      icon: Icons.TypeIcon,
      value: taskLog.type,
      editValue: editForm.type,
      placeholder: 'Enter task type',
    },
    {
      key: 'escalationPolicy' as keyof TaskLogDetailsFormData,
      label: 'Escalation Policy',
      icon: Icons.EscalationPolicyIcon,
      value: taskLog.escalationPolicy,
      editValue: editForm.escalationPolicy,
      placeholder: 'Enter escalation policy',
    },
  ];

  return (
    <div className="space-y-4">
      {fieldConfig.map(
        ({ key, label, icon: Icon, value, editValue, placeholder }) => (
          <div
            key={key}
            className={clsx(
              'rounded border p-4',
              isEditing
                ? 'border-primary bg-[#FFFAF7]'
                : 'border-0 bg-[#FFFAF7]'
            )}
          >
            <div className="flex items-center gap-3">
              <Icon className="h-5 w-5 text-primary" />
              <span className="text-sm font-medium text-[#0F0006]">
                {label}:
              </span>
              {isEditing ? (
                <input
                  type="text"
                  value={editValue || ''}
                  onChange={e => onInputChange(key, e.target.value)}
                  className="flex-1 border-0 bg-transparent py-0 text-gray-900 outline-none"
                  placeholder={placeholder}
                />
              ) : (
                <span className="capitalize text-subText">{value}</span>
              )}
            </div>
          </div>
        )
      )}
    </div>
  );
};

export default TaskLogAdditionalFields;
