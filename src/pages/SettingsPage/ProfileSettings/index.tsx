import React from 'react';
import { Route, Routes } from 'react-router-dom';

import NotFoundPage from '../../NotFoundPage';
import { ChangeEmail } from './ChangeEmail';
import { ChangePassword } from './ChangePassword';
import { ProfileForm } from './ProfileForm';

export const ProfileSettings: React.FC = () => {
  return (
    <Routes>
      <Route index element={<ProfileForm />} />
      <Route path="change-email" element={<ChangeEmail />} />
      <Route path="change-password" element={<ChangePassword />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};
