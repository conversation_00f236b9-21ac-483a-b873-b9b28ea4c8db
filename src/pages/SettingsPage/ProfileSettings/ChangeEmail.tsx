import { AnimatePresence, motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import { regis } from '@/assets/images';
import { EmailChangeForm } from '@/components/common/EmailChangeForm';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { OTPVerification } from '@/components/common/OTPVerification';
import Alert from '@/components/ui/Alert';
import { useAuth } from '@/context/AuthContext';
import { useTenant } from '@/context/TenantContext';
import {
  useChangeEmailMutation,
  useRequestTokenMutation,
  useRequestUnregisteredEmailTokenMutation,
  useValidateTokenMutation,
  useVerifyUnregisteredEmailTokenMutation,
} from '@/hooks/useUserProfile';

import { ROUTES } from '../../../constants/routes';

export const ChangeEmail: React.FC = () => {
  const { user } = useAuth();
  const { setActiveAgent } = useTenant();
  const currentEmail = user?.email || '';
  const navigate = useNavigate();
  const changeEmailMutation = useChangeEmailMutation();
  const requestTokenMutation = useRequestTokenMutation();
  const validateTokenMutation = useValidateTokenMutation();
  const requestUnregisteredEmailTokenMutation =
    useRequestUnregisteredEmailTokenMutation();
  const verifyUnregisteredEmailTokenMutation =
    useVerifyUnregisteredEmailTokenMutation();

  const [step, setStep] = useState<'otp1' | 'newEmail' | 'otp2'>('otp1');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [chatMessage] = useState<string>('');
  const [isResendLoading, setIsResendLoading] = useState(false);
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  // Set active agent to Regis when component mounts
  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent]);

  const handleOTP1Submit = async (code: string) => {
    setError(null);

    try {
      // Validate the OTP token for registered email
      await validateTokenMutation.mutateAsync(code);

      // Store the validated OTP for later use
      sessionStorage.setItem('registeredEmailOTP', code);

      // Move to new email input step
      setStep('newEmail');
    } catch (err: any) {
      // Extract error message from API response
      let errorMessage = 'Failed to validate OTP';

      if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    }
  };

  const handleNewEmailSubmit = async (newEmailAddress: string) => {
    setError(null);

    try {
      // Request token for unregistered email
      await requestUnregisteredEmailTokenMutation.mutateAsync(newEmailAddress);

      // Store new email for later use
      sessionStorage.setItem('newEmailAddress', newEmailAddress);

      // Move to second OTP verification step
      setStep('otp2');
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Failed to request verification token for new email';
      setError(errorMessage);
    }
  };

  const handleOTP2Submit = async (code: string) => {
    setError(null);
    setSuccess(null);

    try {
      // Verify the OTP token for unregistered email
      await verifyUnregisteredEmailTokenMutation.mutateAsync(code);

      // Get stored data
      const storedNewEmail = sessionStorage.getItem('newEmailAddress');
      const storedRegisteredOTP = sessionStorage.getItem('registeredEmailOTP');

      if (!storedNewEmail || !storedRegisteredOTP) {
        throw new Error('Required verification data not found');
      }

      // Change the email with the registered email token
      await changeEmailMutation.mutateAsync({
        email: storedNewEmail,
        token: storedRegisteredOTP,
      });

      // Clean up session storage
      sessionStorage.removeItem('newEmailAddress');
      sessionStorage.removeItem('registeredEmailOTP');

      setSuccess('Email changed successfully!');

      // Navigate back to profile after 2 seconds
      setTimeout(() => {
        navigate('/dashboard/settings/profile');
      }, 2000);
    } catch (err: any) {
      // Extract error message from API response
      let errorMessage = 'Failed to verify OTP or change email';

      if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    }
  };

  const handleResendCode = async () => {
    setError(null);
    setIsResendLoading(true);

    try {
      if (step === 'otp1') {
        await requestTokenMutation.mutateAsync();
      } else if (step === 'otp2') {
        const storedEmail = sessionStorage.getItem('newEmailAddress');
        if (storedEmail) {
          await requestUnregisteredEmailTokenMutation.mutateAsync(storedEmail);
        }
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Failed to resend verification code';
      setError(errorMessage);
    } finally {
      setIsResendLoading(false);
    }
  };

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalInjectedMessage={chatMessage}
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="relative flex-1 overflow-hidden p-6">
        <div className="flex items-center space-x-1">
          <Link
            to={ROUTES.DASHBOARD_SETTINGS_PROFILE}
            className="cursor-pointer text-sm font-normal text-primary underline sm:text-base lg:text-lg"
          >
            My Profile
          </Link>
          <ChevronRight className="h-4 w-4 text-subText" strokeWidth={3} />
          <h3 className="text-sm font-normal text-subText sm:text-base lg:text-lg">
            Change email
          </h3>
        </div>

        {/* Alert - Relative to this section */}
        {error && (
          <div className="mt-4">
            <Alert
              message={error}
              type="error"
              clearMode="manual"
              onClose={() => setError(null)}
              agent={{
                name: 'Regis',
                avatar: regis,
              }}
              showIcon={true}
            />
          </div>
        )}

        {success && (
          <div className="mt-4">
            <Alert
              message={success}
              type="success"
              clearMode="manual"
              onClose={() => setSuccess(null)}
              agent={{
                name: 'Regis',
                avatar: regis,
              }}
              showIcon={true}
            />
          </div>
        )}

        <AnimatePresence mode="wait">
          {step === 'otp1' ? (
            <motion.div
              key="otp1"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="h-full w-full"
            >
              <OTPVerification
                title="Verify Current Email"
                isLoading={validateTokenMutation.isPending}
                error={null}
                success={success}
                onSubmit={handleOTP1Submit}
                onResend={handleResendCode}
                setError={setError}
                isResendLoading={isResendLoading}
              />
            </motion.div>
          ) : step === 'newEmail' ? (
            <motion.div
              key="newEmail"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="h-full w-full overflow-y-auto"
            >
              <EmailChangeForm
                currentEmail={currentEmail}
                isLoading={requestUnregisteredEmailTokenMutation.isPending}
                error={error}
                onSubmit={handleNewEmailSubmit}
                setError={setError}
              />
            </motion.div>
          ) : (
            <motion.div
              key="otp2"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="h-full w-full"
            >
              <OTPVerification
                title="Verify New Email"
                isLoading={verifyUnregisteredEmailTokenMutation.isPending}
                error={null}
                success={success}
                onSubmit={handleOTP2Submit}
                onResend={handleResendCode}
                setError={setError}
                isResendLoading={isResendLoading}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
