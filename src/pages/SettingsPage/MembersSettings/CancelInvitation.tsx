import { Loader2 } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui';

import AnimatedModal from '../../../components/common/AnimatedModal';
import { Invitation } from '../../../types/members';

interface CancelInvitationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCancel: (invitation: Invitation) => void;
  invitation: Invitation | null;
  loading?: boolean;
}

const CancelInvitationModal: React.FC<CancelInvitationModalProps> = ({
  isOpen,
  onClose,
  onCancel,
  invitation,
  loading = false,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!invitation) return;
    onCancel(invitation);
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!invitation}
      onClose={onClose}
      maxWidth="lg"
      showCloseButton={false}
    >
      <div className="flex h-[270px] flex-col items-center justify-center gap-5 p-6 text-center">
        <h3 className="text-lg font-semibold text-blackOne">
          Cancel invitation for {invitation?.firstname} {invitation?.lastname}?
        </h3>
        <p className="text-sm text-subText sm:text-base">
          This person will not be able to join your team unless you invite them
          again.
        </p>
        <form onSubmit={handleSubmit}>
          <div className="flex justify-center space-x-3">
            <Button
              type="button"
              onClick={onClose}
              className="hover:bg-graySixteen/80 rounded-lg border border-graySixteen bg-graySixteen px-4 py-2 text-sm font-medium text-white"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex items-center justify-center rounded-lg border border-delete bg-delete px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2">Cancelling...</span>
                </>
              ) : (
                'Cancel Invitation'
              )}
            </Button>
          </div>
        </form>
      </div>
    </AnimatedModal>
  );
};

export default CancelInvitationModal;
