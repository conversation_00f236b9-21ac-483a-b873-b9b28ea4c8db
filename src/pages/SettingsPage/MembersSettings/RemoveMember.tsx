import { Loader2 } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui';

import { Icons } from '../../../assets/icons/DashboardIcons';
import AnimatedModal from '../../../components/common/AnimatedModal';
import { MemberRole } from '../../../types/members';

interface RemoveMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRemoveMember: (memberId: string) => void;
  member: {
    id: string;
    name: string;
    role: MemberRole;
  } | null;
  loading?: boolean;
}

const RemoveMemberModal: React.FC<RemoveMemberModalProps> = ({
  isOpen,
  onClose,
  onRemoveMember,
  member,
  loading = false,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!member) return;
    onRemoveMember(member.id);
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!member}
      onClose={onClose}
      maxWidth="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col items-center justify-center text-center">
        <div className="flex flex-col items-center justify-center gap-5 p-6">
          <div className="flex h-[52px] w-[52px] flex-shrink-0 items-center justify-center rounded-lg bg-yellowOne">
            <Icons.RemoveMember className="h-9 w-9" />
          </div>
          {/* Title */}
          <h3 className="text-lg font-semibold text-blackOne">
            Remove {member?.name}?
          </h3>

          {/* Description */}
          <p className="text-sm text-subText sm:text-base">
            {member?.name} will no longer have access to SetIQ's features and
            dashboards.
          </p>
        </div>

        {/* Actions */}
        <form onSubmit={handleSubmit} className="w-full border-t">
          <div className="flex w-full items-center justify-center space-x-4 p-6">
            <Button
              type="button"
              onClick={onClose}
              className="h-10 w-full rounded-lg border border-grayThirteen bg-white px-4 py-2.5 text-sm text-blackOne"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex h-10 w-full items-center justify-center rounded-lg border border-delete bg-delete px-4 py-2.5 text-sm text-white hover:bg-red-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2">Removing...</span>
                </>
              ) : (
                'Remove Member'
              )}
            </Button>
          </div>
        </form>
      </div>
    </AnimatedModal>
  );
};

export default RemoveMemberModal;
