import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import { regis } from '@/assets/images';
import AppContainer from '@/components/common/AppContainer';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import AgentSkeleton from '@/components/ui/AgentSkeleton';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { agentCategories, agentSuites as mockAgents } from '@/data/constants';
import { useGetAIAgentsData } from '@/hooks/useAgents';

const MarketplacePage = () => {
  const { activeAgent, setActiveAgent } = useTenant();
  const navigate = useNavigate();
  // Ensure the overall page is scrolled to top on initial navigation to this page.
  useEffect(() => {
    // Use a setTimeout to allow the route transition to complete before forcing scroll.
    const id = window.setTimeout(() => {
      try {
        window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
      } catch (e) {
        window.scrollTo(0, 0);
      }
    }, 0);

    return () => window.clearTimeout(id);
  }, []);

  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent, activeAgent]);

  const [selectedCategory, setSelectedCategory] = useState('all');
  const [mobileView, setMobileView] = useState<'chat' | 'marketplace'>(
    'marketplace'
  );

  const handleAgentsFilter = (categoryId: string) => {
    if (categoryId === selectedCategory) {
      setSelectedCategory('all');
    } else {
      setSelectedCategory(categoryId);
    }
  };

  const { agents, agentSuites, isLoadingAgents, isLoadingSuites } =
    useGetAIAgentsData();

  // Compute filtered agents based on selected category
  const agentsList =
    selectedCategory === 'all'
      ? agents
      : agents.filter(agent =>
          agent.categories.some(c => c === selectedCategory)
        );

  return (
    <div className="min-h-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="mx-auto h-full max-w-screen-2xl"
      >
        <div className="max-h-screen font-inter">
          {/* Mobile Toggle Buttons */}
          <div className="mb-2 flex border-y p-2 md:hidden">
            <button
              onClick={() => setMobileView('marketplace')}
              className={`flex flex-1 items-center justify-center gap-2 rounded-l-lg px-4 py-3 text-sm font-medium transition-colors ${
                mobileView === 'marketplace'
                  ? 'border-b-2 border-primary bg-primary text-white'
                  : 'bg-orange-50/85 text-blackOne hover:text-gray-900'
              }`}
            >
              <Icons.Agent className="h-4 w-4" />
              Agents Hub
            </button>
            <button
              onClick={() => setMobileView('chat')}
              className={`flex flex-1 items-center justify-center gap-2 rounded-r-lg px-4 py-3 text-sm font-medium transition-colors ${
                mobileView === 'chat'
                  ? 'border-b-2 border-primary bg-primary text-white'
                  : 'bg-orange-50/85 text-blackOne hover:text-gray-900'
              }`}
            >
              <Icons.Chat2 />
              Chat
            </button>
          </div>

          <div className="flex h-full flex-1 gap-8 overflow-hidden md:h-[calc(100vh-100px)]">
            {/* Chat Sidebar */}
            <div
              className={`${mobileView === 'chat' ? 'block' : 'hidden'} md:block`}
            >
              <EnhancedChatSidebar />
            </div>

            {/* Agents Marketplace */}
            <div
              className={`flex-1 overflow-y-auto ${mobileView === 'marketplace' ? 'block' : 'hidden'} md:block`}
            >
              <AppContainer
                className="space-y-4 px-4 sm:space-y-6 sm:px-6 lg:space-y-8"
                isPadding={false}
              >
                <div className="mb-6 flex w-fit flex-wrap justify-center gap-2 bg-blue-midnight p-3 sm:p-4">
                  {agentCategories.map((category, index) => (
                    <button
                      key={index}
                      className={`rounded-md ${
                        selectedCategory === category.id
                          ? 'bg-primary text-white hover:bg-darkOrangeTwo'
                          : 'bg-grayFifteen text-blackOne hover:bg-blue-50'
                      } touch-manipulation px-3 py-2 font-inter text-xs font-medium transition sm:px-4 sm:py-2.5 sm:text-sm`}
                      onClick={() => handleAgentsFilter(category.id)}
                    >
                      {category.alias}
                    </button>
                  ))}
                </div>

                <div
                  className={`flex gap-4 ${agentsList.length === 0 && 'items-center justify-center'}`}
                >
                  {isLoadingSuites ? (
                    Array.from({ length: 2 }).map((_, index) => (
                      <AgentSkeleton key={`skeleton-${index}`} />
                    ))
                  ) : (
                    <div className="grid h-fit grid-cols-1 gap-4">
                      {agentSuites.map(suite => (
                        <div
                          key={suite.agentSuiteKey}
                          className="flex h-[338px] w-full max-w-[280px] cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md sm:w-[240px]"
                          onClick={() => {
                            setActiveAgent('');
                            navigate(
                              ROUTES.AGENTS_DETAILS(suite.agentSuiteKey)
                            );
                          }}
                        >
                          <div>
                            <img
                              src={suite.avatar}
                              className="h-[160px] w-full bg-peachTwo object-cover"
                              alt={suite.agentSuiteName}
                              onError={e => {
                                const agentKey =
                                  suite.agentSuiteKey.toLowerCase();

                                // Fallback to mock logo if agent avatar fails to load
                                (e.target as HTMLImageElement).src =
                                  mockAgents.filter(
                                    agent => agent.id.toLowerCase() === agentKey
                                  )[0].image;
                              }}
                            />
                            <div className="flex flex-col gap-2 px-2.5 py-3 text-blackOne md:gap-2.5">
                              <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] text-sm font-bold md:text-base">
                                {suite.agentSuiteName}
                              </div>
                              <p className="text-sm font-bold leading-5 md:text-base md:font-medium">
                                {suite.description}
                              </p>
                              <p className="mb-3 text-sm text-darkGray">
                                {suite.roleDescription}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}

                      <div
                        className="flex h-[338px] w-full max-w-[280px] cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md sm:w-[240px]"
                        onClick={() => {
                          setActiveAgent('regis');
                          setMobileView('chat');
                        }}
                      >
                        <img
                          src={regis}
                          className="h-[160px] w-full bg-[#587C72] object-contain"
                          alt="Regis"
                        />
                        <div className="flex flex-col items-center px-8 py-4 text-blackOne">
                          <div className="w-fit rounded-lg border border-blackOne px-1.5 py-1 font-bold">
                            <div className="hidden md:block">
                              <ArrowLeft />
                            </div>
                            <div className="block md:hidden">
                              <ArrowRight />
                            </div>
                          </div>
                          <p className="mt-3 text-base md:text-lg">
                            Chat with Regis
                          </p>
                          <p className="text-center text-lg font-semibold text-darkGray md:text-2xl">
                            See what's next?
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {isLoadingAgents ? (
                    Array.from({ length: 4 }).map((_, index) => (
                      <AgentSkeleton key={`skeleton-${index}`} />
                    ))
                  ) : (
                    <div className="flex w-fit justify-center">
                      <div className="grid grid-cols-1 justify-items-center gap-4 sm:grid-cols-2">
                        {agentsList.map(agent => (
                          <div
                            key={agent.agentKey}
                            className="flex h-[338px] w-full max-w-[280px] cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md sm:w-[240px]"
                          >
                            <div
                              onClick={() => {
                                agent.agentKey
                                  ? setActiveAgent(agent.agentKey)
                                  : setActiveAgent('');

                                agent.agentSuiteKey &&
                                  navigate(
                                    ROUTES.AGENTS_DETAILS(agent.agentSuiteKey)
                                  );
                              }}
                            >
                              <img
                                src={agent.avatar}
                                className="h-[160px] w-full bg-peachTwo object-contain"
                                alt={agent.agentName}
                                onError={e => {
                                  const agentKey = agent.agentKey.toLowerCase();

                                  // Fallback to mock logo if agent avatar fails to load
                                  (e.target as HTMLImageElement).src =
                                    mockAgents.filter(
                                      agent =>
                                        agent.id.toLowerCase() === agentKey
                                    )[0].image;
                                }}
                              />
                              <div className="flex flex-col gap-2 p-3 text-blackOne md:gap-2.5">
                                <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] text-sm font-bold md:text-base">
                                  {agent.agentName}
                                </div>
                                <p className="text-sm font-bold leading-5 md:text-base md:font-medium">
                                  {agent.description}
                                </p>
                                <p className="mb-3 text-sm text-darkGray">
                                  {agent.roleDescription}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </AppContainer>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default MarketplacePage;
