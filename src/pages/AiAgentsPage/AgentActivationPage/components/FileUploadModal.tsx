import {
  CheckCircle,
  File,
  FileText,
  Image,
  Presentation,
  Trash2,
  X,
  XCircle,
} from 'lucide-react';
import React, { useCallback, useState } from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import AnimatedModal from '@/components/common/AnimatedModal';

interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFilesUpload: (files: File[]) => void;
  title: string;
  multiple?: boolean;
}

const FileUploadModal: React.FC<FileUploadModalProps> = ({
  isOpen,
  onClose,
  title,
  onFilesUpload,
  multiple = true,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedFiles] = useState<string[]>([]);
  const [uploadingFiles] = useState<{ name: string; progress: number }[]>([]);
  const [errorFiles] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState<boolean>(false);

  const handleFileSelect = useCallback(
    (files: File[]) => {
      setSelectedFiles(prevFiles =>
        multiple ? [...prevFiles, ...files] : files.slice(0, 1)
      );
    },
    [multiple]
  );

  const handleFileDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setDragActive(false);
      const droppedFiles = Array.from(e.dataTransfer.files);
      handleFileSelect(droppedFiles);
    },
    [handleFileSelect]
  );

  const handleBrowseFiles = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = multiple;
    input.accept = '.jpeg,.jpg,.png,.pdf,.ai,.doc,.docx,.ppt,.pptx';
    input.onchange = e => {
      const files = Array.from((e.target as HTMLInputElement).files || []);
      handleFileSelect(files);
    };
    input.click();
  }, [handleFileSelect, multiple]);

  const handleUpload = useCallback(() => {
    if (selectedFiles.length > 0) {
      onFilesUpload(selectedFiles);
      setSelectedFiles([]);
      onClose();
    }
  }, [selectedFiles, onFilesUpload, onClose]);

  const removeFile = useCallback((index: number) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileType = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'ppt':
      case 'pptx':
        return 'ppt';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'img';
      case 'ai':
        return 'ai';
      default:
        return 'file';
    }
  };

  const getFileIcon = (fileName: string) => {
    const fileType = getFileType(fileName);
    switch (fileType) {
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-500" />;
      case 'doc':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'ppt':
        return <Presentation className="h-4 w-4 text-orange-500" />;
      case 'img':
        return <Image className="text-green-500 h-4 w-4" />;
      default:
        return <File className="h-4 w-4 text-gray-500" />;
    }
  };

  const countPdfPages = async (file: File): Promise<number> => {
    if (getFileType(file.name) !== 'pdf') {
      return 0;
    }

    try {
      // For demo purposes, return a simulated page count
      // In a real implementation, you'd use a PDF parsing library
      const sizeInMB = file.size / (1024 * 1024);
      // Rough estimate: ~1 page per 100KB for typical PDFs
      return Math.max(1, Math.round(sizeInMB * 10));
    } catch (error) {
      console.error('Error counting PDF pages:', error);
      return 0;
    }
  };

  const getFileInfo = async (file: File): Promise<string> => {
    const fileType = getFileType(file.name);
    const size = formatFileSize(file.size);

    if (fileType === 'pdf') {
      const pages = await countPdfPages(file);
      return `${pages} pages • ${size} • ${fileType}`;
    }

    return `${size} • ${fileType}`;
  };

  // Component to display file info with async loading
  const FileInfoDisplay: React.FC<{ file: File }> = ({ file }) => {
    const [fileInfo, setFileInfo] = useState<string>('');
    const [loading, setLoading] = useState(true);

    React.useEffect(() => {
      const loadFileInfo = async () => {
        setLoading(true);
        const info = await getFileInfo(file);
        setFileInfo(info);
        setLoading(false);
      };
      loadFileInfo();
    }, [file]);

    if (loading) {
      return <span className="text-xs text-gray-500">Loading...</span>;
    }

    return <span className="text-xs text-gray-500">{fileInfo}</span>;
  };

  if (!isOpen) return null;

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={onClose}
      showCloseButton={false}
      maxWidth="xl"
    >
      <div className="mx-auto w-full space-y-6 py-10 md:w-[445px]">
        <div className="flex flex-col items-center justify-center gap-6 text-center">
          <div className="flex items-center">
            <h3 className="text-[32px] font-bold text-black">{title}</h3>
          </div>
          <div>
            <h3 className="mb-6 text-[22px] font-bold text-black">Upload</h3>

            {/* Drag & Drop Area */}
            <div
              className={`relative w-full rounded-md border-2 border-dashed p-12 transition-colors ${
                dragActive
                  ? 'border-primary bg-primary/5'
                  : 'border-[#384EB74D] bg-[#F8F8FF]'
              }`}
              onDragEnter={e => {
                e.preventDefault();
                setDragActive(true);
              }}
              onDragLeave={e => {
                e.preventDefault();
                setDragActive(false);
              }}
              onDragOver={e => e.preventDefault()}
              onDrop={handleFileDrop}
            >
              <div className="flex flex-col items-center">
                <Icons.Upload className="mb-6 h-[59.58695602416992px] w-[68.78290557861328px]" />
                <p className="mb-2 text-base text-black">
                  Drag & drop {multiple ? 'files' : 'a file'} or{' '}
                  <button
                    className="font-semibold text-primary underline hover:text-primary/80"
                    onClick={handleBrowseFiles}
                  >
                    Browse
                  </button>
                </p>
                <p className="text-sm text-grayTen">
                  Supported formats: JPEG, PNG, PDF, AI, Word, PPT
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Upload Progress */}
        {uploadingFiles.length > 0 && (
          <div className="space-y-3">
            <p className="text-sm font-medium text-black">
              Uploading - {uploadingFiles.length}/3 files
            </p>
            {uploadingFiles.map((file, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{file.name}</span>
                  <button className="text-gray-400 hover:text-gray-600">
                    <X className="h-4 w-4" />
                  </button>
                </div>
                <div className="h-2 w-full rounded-full bg-gray-200">
                  <div
                    className="h-2 rounded-full bg-primary transition-all duration-300"
                    style={{ width: `${file.progress}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Uploaded Files */}
        {uploadedFiles.length > 0 && (
          <div className="space-y-3">
            <p className="text-sm font-medium text-black">Uploaded</p>
            {uploadedFiles.map((fileName, index) => (
              <div
                key={index}
                className="flex items-center justify-between rounded-lg border border-gray-300 p-3"
              >
                <div className="flex items-center gap-3">
                  <CheckCircle className="text-green-500 h-5 w-5" />
                  <span className="text-sm text-gray-700">{fileName}</span>
                </div>
                <button className="text-red-500 hover:text-red-700">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Error Files */}
        {errorFiles.length > 0 && (
          <div className="space-y-3">
            <p className="text-sm font-medium text-black">Error</p>
            {errorFiles.map((fileName, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between rounded-lg border border-red-300 bg-red-50 p-3">
                  <div className="flex items-center gap-3">
                    <XCircle className="h-5 w-5 text-red-500" />
                    <span className="text-sm text-gray-700">{fileName}</span>
                  </div>
                  <button className="text-red-500 hover:text-red-700">
                    <X className="h-4 w-4" />
                  </button>
                </div>
                <p className="text-xs text-red-600">
                  This document is not supported, please delete and upload
                  another file.
                </p>
              </div>
            ))}
          </div>
        )}

        {/* Selected Files */}
        {selectedFiles.length > 0 && (
          <div className="space-y-3">
            <p className="text-sm font-medium text-black">
              Selected {multiple ? 'Files' : 'File'} ({selectedFiles.length})
            </p>
            {selectedFiles.map((file, index) => (
              <div key={index} className="space-y-1">
                <div className="flex items-center justify-between rounded-xl bg-[#EAEAEA] p-2">
                  <div className="flex items-center gap-3">
                    <span className="text-base">{getFileIcon(file.name)}</span>
                    <div className="min-w-0 flex-1">
                      <p className="line-clamp-1 truncate text-xs font-medium text-[#46484B]">
                        {file.name}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => removeFile(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
                <p className="px-2 text-xs text-[#6B7280]">
                  <FileInfoDisplay file={file} />
                </p>
              </div>
            ))}
          </div>
        )}

        {/* Upload Button */}
        <button
          onClick={handleUpload}
          disabled={selectedFiles.length === 0}
          className="h-[48px] w-full rounded-md bg-primary px-4 py-3 text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:bg-gray-300"
        >
          Upload {multiple ? `Files (${selectedFiles.length})` : 'File'}
        </button>
      </div>
    </AnimatedModal>
  );
};

export default FileUploadModal;
