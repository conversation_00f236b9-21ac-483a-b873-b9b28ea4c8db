import { AxiosError } from 'axios';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Button } from '@/components/ui/Button';
import { useAuthEventContext } from '@/context/AuthEventContext';
import { useGetLinkForPasswordReset } from '@/hooks/useAuth';
import { emailReplaceCharacterRegex } from '@/utils/helpers';

export default function GetLinkStepTwo() {
  const navigate = useNavigate();
  const { email } = useAuthEventContext();
  const { isPending: isLoading, mutateAsync } = useGetLinkForPasswordReset();
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');

  useEffect(() => {
    navigate('/reset-password?n_t=2');
  }, []);

  // Clear messages after 3 seconds
  useEffect(() => {
    if (errorMessage || successMessage) {
      const timer = setTimeout(() => {
        setErrorMessage('');
        setSuccessMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage, successMessage]);

  const handleResendLink = async () => {
    if (email?.email) {
      setErrorMessage('');
      setSuccessMessage('');

      try {
        await mutateAsync({ email: email.email });
        setSuccessMessage('Password reset link has been resent successfully!');
      } catch (error) {
        // Extract error message from axios error response
        const axiosError = error as AxiosError<{ message?: string }>;
        const message =
          axiosError.response?.data?.message ||
          'Failed to resend password reset link';
        setErrorMessage(message);
      }
    }
  };
  return (
    <motion.div
      exit={{ opacity: 0 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      className="flex-1 px-4 sm:px-0"
    >
      <div className="mx-auto h-full w-full max-w-[457px] py-8 sm:py-20">
        <h2 className="mb-3 pt-12 text-center text-[24px] font-[500] leading-[47px] sm:pt-24 sm:text-[32px]">
          We have sent an Email!
        </h2>
        <p className="pb-12 text-center text-[16px] leading-[27px]">
          We've sent a password reset link to your registered email address{' '}
          {emailReplaceCharacterRegex(email?.email || '')}.
        </p>
        {errorMessage && (
          <p className="mb-4 text-center text-sm text-red-600">
            {errorMessage}
          </p>
        )}
        {successMessage && (
          <p className="text-green-600 mb-4 text-center text-sm">
            {successMessage}
          </p>
        )}
        <p className=" pb-12 text-center text-[16px] leading-[27px]">
          Didn't receive the email? Check spam or promotion folder or{' '}
          <span
            onClick={handleResendLink}
            className="cursor-pointer text-primary underline"
          >
            {isLoading ? 'Sending...' : 'Click to resend.'}
          </span>
        </p>
        <Button
          type="button"
          onClick={() => {
            window.open(
              `https://mail.google.com/mail/u/${email.email}`,
              '_blank'
            );
          }}
          className="mx-auto  mt-[32px]
          w-full border   
          border-primary bg-transparent text-primary hover:bg-primary hover:text-white"
        >
          {' '}
          <p className="text-[16px] font-[700]">Proceed to reset password</p>
        </Button>
      </div>
    </motion.div>
  );
}
