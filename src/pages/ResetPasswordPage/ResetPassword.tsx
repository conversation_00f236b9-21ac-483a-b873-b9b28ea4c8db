import { AnimatePresence } from 'framer-motion';

import { AuthEventProvider } from '@/context/AuthEventContext';

import GetLinkStepOneForm from './GetLinkStepOneForm';
import GetLinkStepTwo from './GetLinkStepTwo';
import ResetPasswordContainer from './ResetPasswordContainer';

export function ResetPassword() {
  return (
    <AuthEventProvider>
      <div className="mx-auto h-screen overflow-hidden">
        <AnimatePresence>
          <ResetPasswordContainer level={1} key="stepOne">
            <GetLinkStepOneForm />
          </ResetPasswordContainer>
          <ResetPasswordContainer level={2} key="stepTwo">
            <GetLinkStepTwo />
          </ResetPasswordContainer>
        </AnimatePresence>
      </div>
    </AuthEventProvider>
  );
}
