import { useKeycloak } from '@react-keycloak/web';
import type { AxiosInstance } from 'axios';
import axios from 'axios';

import { BASE_URL } from '@/utils/apiUrls';
import { isDevEnvironment } from '@/utils/helpers';

// Create base axios instance without auth
const createBaseAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: BASE_URL,
    timeout: 120000,
    maxContentLength: Infinity,
    maxBodyLength: Infinity,
  });

  instance.defaults.headers.common['Save-Data'] = 'on';

  instance.interceptors.response.use(
    response => response,
    async error => {
      if (error.code === 'ECONNABORTED') {
        console.warn('Request timed out. Retrying...');
        return instance?.request(error.config);
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

// This function is deprecated - use usePrivateRequest hook instead
// Keeping it for backward compatibility with dev environment
export const getDevAccessToken = (): string => {
  return import.meta.env.VITE_DEV_ACCESS_TOKEN || '';
};

// Base instance for non-authenticated requests
export const axiosInstance = createBaseAxiosInstance();

// Hook to get auth token
export const useAuthToken = (): string | undefined => {
  const { keycloak, initialized } = useKeycloak();
  return initialized || isDevEnvironment()
    ? (keycloak?.token ?? import.meta.env.VITE_DEV_ACCESS_TOKEN)
    : undefined;
};

// Hook to get authenticated axios instance
export const useAuthenticatedAxios = () => {
  // This will be properly implemented in a hook that uses useKeycloak
  return axiosInstance;
};

// Temporary wrapper for backward compatibility - will be removed
export const createAuthenticatedAxiosInstance = useAuthToken;
