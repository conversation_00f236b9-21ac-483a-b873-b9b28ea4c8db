export interface AssignmentLogAttachment {
  name: string;
  link: string;
  type: string;
  source: string;
}

export interface AssignmentLogAuditEntry {
  timestamp: string;
  actor: string;
  event: string;
  notes: string;
}

export type AssignmentLogStatus =
  | 'PENDING'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED';
export type AssignmentLogPriority =
  | 'LOW'
  | 'MEDIUM'
  | 'HIGH'
  | 'CRITICAL'
  | 'URGENT';

export interface AssignmentLogTaskTypeRef {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  name: string;
  description: string;
}

export interface AssignmentLog {
  id: string;
  assignmentId: string;
  tenantId: string;
  assignedDate: string;
  assignedBy: string;
  assignedTo: string;
  description: string;
  startDate: string;
  dueDate: string;
  status: AssignmentLogStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface AssignmentLogFilter {
  search?: string;
  createdBy: string;
  tenantId: string;
  status?: AssignmentLogStatus;
  assignedDateFrom?: string;
  assignedDateTo?: string;
  startDateFrom?: string;
  startDateTo?: string;
  page?: number;
  pageSize?: number;
}

export interface AssignmentLogListResponse {
  status: boolean;
  message: string;
  data: {
    assignments: AssignmentLog[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface AssignmentLogResponse {
  status: boolean;
  message: string;
  data: AssignmentLog;
}

// Display versions for UI
export type AssignmentLogStatusDisplay =
  | 'Pending'
  | 'In Progress'
  | 'Completed'
  | 'Cancelled';

export type AssignmentLogCheckInsDisplay =
  | 'Hourly'
  | 'Daily'
  | 'Weekly'
  | 'Quarterly'
  | 'Monthly';

// Mapping functions for API to UI conversion
export const mapAssignmentStatusToDisplay = (
  status: AssignmentLogStatus
): AssignmentLogStatusDisplay => {
  const statusMap: Record<AssignmentLogStatus, AssignmentLogStatusDisplay> = {
    PENDING: 'Pending',
    IN_PROGRESS: 'In Progress',
    COMPLETED: 'Completed',
    CANCELLED: 'Cancelled',
  };
  return statusMap[status];
};

export const mapAssignmentDisplayToStatus = (
  display: AssignmentLogStatusDisplay
): AssignmentLogStatus => {
  const displayMap: Record<AssignmentLogStatusDisplay, AssignmentLogStatus> = {
    Pending: 'PENDING',
    'In Progress': 'IN_PROGRESS',
    Completed: 'COMPLETED',
    Cancelled: 'CANCELLED',
  };
  return displayMap[display];
};

// UI specific types for the assignment details page
export interface AssignmentLogDetailsFormData {
  assignmentTitle?: string;
  description: string;
  time: string;
  dateCreated: string;
  dueDate: string;
  assignedTo?: string;
  createdBy: string;
  type: string | null;
  checkIns: AssignmentLogCheckInsDisplay | null;
  status: AssignmentLogStatusDisplay;
  escalationPolicy: string | null;
}
