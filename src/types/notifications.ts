/**
 * Notification Toast Component Types
 *
 * This file contains TypeScript interfaces and types for the notification toast system.
 * The notification system supports inline notifications with agent information,
 * messages, optional images, and close functionality.
 */

/**
 * Agent information for notifications
 * Can be provided explicitly or will use active agent from context
 */
export interface NotificationAgent {
  /** Display name of the agent */
  agentName: string;
  /** Avatar/profile image URL for the agent */
  avatar: string;
}

/**
 * Individual notification data structure
 */
export interface NotificationData {
  /** Unique identifier for the notification */
  id: string;
  /** The notification message text to display in quotes */
  message: string;
  /** Optional URL for additional logo/image (e.g., Salesforce logo, SetIQ avatar) */
  additionalImage?: string;
  /** Optional Tailwind CSS classes for styling the additional image */
  additionalImageClassname?: string;
  /** Custom agent override - if not provided, uses activeAgent from useTenant context */
  activeAgent?: NotificationAgent;
  /** Timestamp when notification was created */
  createdAt: Date;
  /** Whether the notification is currently being removed (for exit animations) */
  isRemoving?: boolean;
}

/**
 * Props for the individual NotificationToast component
 */
export interface NotificationToastProps {
  /** The notification data to display */
  notification: NotificationData;
  /** Callback function when the close (X) button is clicked */
  onClose: (id: string) => void;
  /** Optional additional CSS classes for the notification container */
  className?: string;
  size?: 'small' | 'large';
}

/**
 * Props for the NotificationContainer component that manages multiple notifications
 */
export interface NotificationContainerProps {
  /** Array of notifications to display */
  notifications: NotificationData[];
  /** Callback function when a notification is closed */
  onClose: (id: string) => void;
  /** Optional CSS classes for the container */
  className?: string;
  /** Maximum number of notifications to show simultaneously */
  maxNotifications?: number;
  size?: 'small' | 'large';
}

/**
 * Configuration options for adding a new notification
 */
export interface AddNotificationOptions {
  /** The notification message text to display in quotes */
  message: string;
  /** Optional URL for additional logo/image */
  additionalImage?: string;
  /** Optional Tailwind CSS classes for styling the additional image */
  additionalImageClassname?: string;
  /** Custom agent override - if not provided, uses activeAgent from useTenant context */
  activeAgent?: NotificationAgent;
  /** Optional custom ID - if not provided, will be auto-generated */
  id?: string;
}

/**
 * Context value for the notification system
 */
export interface NotificationContextValue {
  /** Array of current notifications */
  notifications: NotificationData[];
  /** Function to add a new notification */
  addNotification: (options: AddNotificationOptions) => string;
  /** Function to remove a notification by ID */
  removeNotification: (id: string) => void;
  /** Function to clear all notifications */
  clearNotifications: () => void;
  /** Whether notifications are currently loading/processing */
  isLoading: boolean;
}

/**
 * Props for the NotificationProvider component
 */
export interface NotificationProviderProps {
  /** Child components */
  children: React.ReactNode;
  /** Maximum number of notifications to keep in state */
  maxNotifications?: number;
  /** Default duration before auto-removal (0 = no auto-removal) */
  autoRemoveDelay?: number;
}

/**
 * Animation states for notification transitions
 */
export type NotificationAnimationState =
  | 'entering'
  | 'entered'
  | 'exiting'
  | 'exited';

/**
 * Notification position options (for future extensibility)
 */
export type NotificationPosition =
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'
  | 'top-center'
  | 'bottom-center'
  | 'inline'; // Default for inline notifications

/**
 * Notification severity levels (for future extensibility)
 */
export type NotificationSeverity = 'info' | 'success' | 'warning' | 'error';

/**
 * Extended notification data with additional metadata
 */
export interface ExtendedNotificationData extends NotificationData {
  /** Animation state for transitions */
  animationState?: NotificationAnimationState;
  /** Position where notification should appear */
  position?: NotificationPosition;
  /** Severity level for styling */
  severity?: NotificationSeverity;
  /** Whether notification should auto-remove */
  autoRemove?: boolean;
  /** Delay before auto-removal in milliseconds */
  autoRemoveDelay?: number;
}
