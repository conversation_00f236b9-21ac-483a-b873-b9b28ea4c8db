// Styles
import 'react-toastify/dist/ReactToastify.css';
import './index.css';

import { ReactKeycloakProvider } from '@react-keycloak/web';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useEffect } from 'react';
import { ToastContainer } from 'react-toastify';

import keycloak from './config/keycloak';
import { AuthProvider } from './context/AuthContext';
import { NotificationProvider } from './context/NotificationContext';
import { TenantProvider } from './context/TenantContext';
import MainAppRoutes from './routes';
import { setupChunkErrorHandler } from './utils/chunkErrorHandler';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  useEffect(() => {
    // Set up global chunk error handler to handle failed dynamic imports
    setupChunkErrorHandler();
  }, []);

  return (
    <ReactKeycloakProvider authClient={keycloak}>
      <QueryClientProvider client={queryClient}>
        <TenantProvider>
          <AuthProvider>
            <NotificationProvider maxNotifications={5}>
              <MainAppRoutes />
              <ToastContainer
                position="top-right"
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="light"
              />
              <ReactQueryDevtools
                initialIsOpen={false}
                position="bottom-right"
              />
            </NotificationProvider>
          </AuthProvider>
        </TenantProvider>
      </QueryClientProvider>
    </ReactKeycloakProvider>
  );
}

export default App;
