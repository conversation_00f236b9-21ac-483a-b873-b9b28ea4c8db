<svg width="60" height="80" viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_63_2192)">
<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="url(#paint0_linear_63_2192)" style=""/>
<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="#DCE2EB" style="fill:#DCE2EB;fill:color(display-p3 0.8633 0.8868 0.9204);fill-opacity:1;"/>
<path d="M8 1H33.5859L59 26.4141V72C59 75.866 55.866 79 52 79H8C4.13401 79 1 75.866 1 72V8C1 4.13401 4.13401 1 8 1Z" stroke="black" stroke-opacity="0.06" style="stroke:black;stroke-opacity:0.06;" stroke-width="2"/>
</g>
<path d="M14.7033 65H9.54702V50.4545H14.7459C16.2089 50.4545 17.4684 50.7457 18.5243 51.3281C19.5802 51.9058 20.3922 52.7367 20.9604 53.821C21.5333 54.9053 21.8197 56.2027 21.8197 57.7131C21.8197 59.2282 21.5333 60.5303 20.9604 61.6193C20.3922 62.7083 19.5754 63.544 18.5101 64.1264C17.4495 64.7088 16.1805 65 14.7033 65ZM12.6223 62.3651H14.5754C15.4845 62.3651 16.2492 62.2041 16.8695 61.8821C17.4945 61.5554 17.9632 61.0511 18.2757 60.3693C18.5929 59.6828 18.7516 58.7973 18.7516 57.7131C18.7516 56.6383 18.5929 55.7599 18.2757 55.0781C17.9632 54.3963 17.4968 53.8944 16.8766 53.5724C16.2563 53.2505 15.4916 53.0895 14.5825 53.0895H12.6223V62.3651ZM37.0643 57.7273C37.0643 59.3134 36.7636 60.6629 36.1623 61.7756C35.5657 62.8883 34.7513 63.7382 33.7191 64.3253C32.6916 64.9077 31.5363 65.1989 30.2532 65.1989C28.9606 65.1989 27.8005 64.9053 26.7731 64.3182C25.7456 63.7311 24.9336 62.8812 24.337 61.7685C23.7404 60.6558 23.4421 59.3087 23.4421 57.7273C23.4421 56.1411 23.7404 54.7917 24.337 53.679C24.9336 52.5663 25.7456 51.7187 26.7731 51.1364C27.8005 50.5492 28.9606 50.2557 30.2532 50.2557C31.5363 50.2557 32.6916 50.5492 33.7191 51.1364C34.7513 51.7187 35.5657 52.5663 36.1623 53.679C36.7636 54.7917 37.0643 56.1411 37.0643 57.7273ZM33.9464 57.7273C33.9464 56.6998 33.7925 55.8333 33.4847 55.1278C33.1817 54.4223 32.7532 53.8873 32.1992 53.5227C31.6452 53.1581 30.9966 52.9759 30.2532 52.9759C29.5098 52.9759 28.8612 53.1581 28.3072 53.5227C27.7532 53.8873 27.3223 54.4223 27.0146 55.1278C26.7115 55.8333 26.56 56.6998 26.56 57.7273C26.56 58.7547 26.7115 59.6212 27.0146 60.3267C27.3223 61.0322 27.7532 61.5672 28.3072 61.9318C28.8612 62.2964 29.5098 62.4787 30.2532 62.4787C30.9966 62.4787 31.6452 62.2964 32.1992 61.9318C32.7532 61.5672 33.1817 61.0322 33.4847 60.3267C33.7925 59.6212 33.9464 58.7547 33.9464 57.7273ZM51.7761 55.5469H48.6653C48.6085 55.1444 48.4925 54.7869 48.3173 54.4744C48.1421 54.1572 47.9172 53.8873 47.6426 53.6648C47.368 53.4422 47.0508 53.2718 46.6909 53.1534C46.3358 53.035 45.9499 52.9759 45.5332 52.9759C44.7804 52.9759 44.1246 53.1629 43.5659 53.5369C43.0072 53.9062 42.574 54.446 42.2662 55.1562C41.9584 55.8617 41.8045 56.7187 41.8045 57.7273C41.8045 58.7642 41.9584 59.6354 42.2662 60.3409C42.5787 61.0464 43.0143 61.5791 43.573 61.9389C44.1317 62.2988 44.778 62.4787 45.5119 62.4787C45.9239 62.4787 46.305 62.4242 46.6554 62.3153C47.0105 62.2064 47.3254 62.0478 47.6 61.8395C47.8746 61.6264 48.1019 61.3684 48.2818 61.0653C48.4665 60.7623 48.5943 60.4167 48.6653 60.0284L51.7761 60.0426C51.6956 60.7102 51.4944 61.3542 51.1724 61.9744C50.8552 62.59 50.4267 63.1416 49.8869 63.6293C49.3519 64.1122 48.7127 64.4957 47.9693 64.7798C47.2307 65.0592 46.395 65.1989 45.4622 65.1989C44.1649 65.1989 43.0048 64.9053 41.9821 64.3182C40.9641 63.7311 40.1592 62.8812 39.5673 61.7685C38.9802 60.6558 38.6866 59.3087 38.6866 57.7273C38.6866 56.1411 38.9849 54.7917 39.5815 53.679C40.1781 52.5663 40.9878 51.7187 42.0105 51.1364C43.0332 50.5492 44.1838 50.2557 45.4622 50.2557C46.305 50.2557 47.0863 50.3741 47.806 50.6108C48.5304 50.8475 49.172 51.1932 49.7307 51.6477C50.2894 52.0975 50.7439 52.6491 51.0943 53.3026C51.4494 53.956 51.6767 54.7041 51.7761 55.5469Z" fill="#344054" style="fill:#344054;fill:color(display-p3 0.2039 0.2510 0.3294);fill-opacity:1;"/>
<defs>
<linearGradient id="paint0_linear_63_2192" x1="30" y1="0" x2="30" y2="80" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2F6FC" style="stop-color:#F2F6FC;stop-color:color(display-p3 0.9476 0.9643 0.9881);stop-opacity:1;"/>
<stop offset="1" stop-color="#EDF1F7" style="stop-color:#EDF1F7;stop-color:color(display-p3 0.9301 0.9467 0.9705);stop-opacity:1;"/>
</linearGradient>
<clipPath id="clip0_63_2192">
<rect width="60" height="80" fill="white" style="fill:white;fill-opacity:1;"/>
</clipPath>
</defs>
</svg>
