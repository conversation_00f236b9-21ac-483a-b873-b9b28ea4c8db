<svg width="53" height="108" viewBox="0 0 53 108" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_14615_172535)">
<circle cx="54" cy="54" r="54" fill="url(#paint0_radial_14615_172535)"/>
</g>
<defs>
<filter id="filter0_i_14615_172535" x="-11" y="-11" width="119" height="119" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-13" dy="-13"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_14615_172535"/>
</filter>
<radialGradient id="paint0_radial_14615_172535" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(54 54) rotate(90) scale(51.1364)">
<stop stop-color="#FFAB95"/>
<stop offset="1" stop-color="#DF3908"/>
</radialGradient>
</defs>
</svg>
