.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  @apply !bg-primary !text-white;
}

.react-datepicker__day--today:not(.react-datepicker__day--selected):not(
    .react-datepicker__day--keyboard-selected
  ) {
  @apply !text-primary;
}

.react-datepicker__input-container input {
  @apply h-[37px] min-h-[37px] w-full max-w-[457] cursor-pointer rounded-md border border-grayFifteen bg-white px-[26px] text-[13px] placeholder-gray-400 outline-[0] transition duration-500 ease-in-out placeholder:text-[13px] focus:border-primary focus:ring-0;
}

.react-datepicker__close-icon::after {
  @apply !bg-primary;
}

.react-datepicker-wrapper {
  @apply w-full;
}
