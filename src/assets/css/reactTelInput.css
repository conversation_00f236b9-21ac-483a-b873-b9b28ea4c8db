/*react-tel-input */
.form-control:focus,
.form-control:active,
.form-control:hover {
  box-shadow: none !important;
  border: 1px solid hsl(var(--primary)) !important;
  background: var(--white) !important;
}
.selected-flag:before {
  border-radius: 12px 0 0 12px !important;
  background: transparent !important;
}

.react-tel-input .selected-flag:focus:before,
.react-tel-input .selected-flag.open:before {
  box-shadow: none !important;
  border: 1px solid var(--grayFifteen) !important;
  background: transparent !important;
}
