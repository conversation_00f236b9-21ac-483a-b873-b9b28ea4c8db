image: node:22-alpine
options:
  docker: true
  size: 2x

pipelines:
  branches:
    dev:
      - parallel:
          - step:
              name: Build and Test
              caches:
                - node
              script:
                - rm -rf package-lock.json
                - rm -rf node_modules
                - npm install --unsafe-perm=true --allow-root cypress
                - npm install --production
                - npm run build
          - step:
              name: Security Scan
              script:
                - pipe: atlassian/git-secrets-scan:0.4.3
      - step:
          name: Build Docker Image and Push to Container Registry
          services:
            - docker
          size: 2x
          script:
            - sed -i "s|{{rest_api_url}}|$REST_API_URL_DEV|g" Dockerfile
            - sed -i "s|{{keycloak_url}}|$KEYCLOAK_URL_DEV|g" Dockerfile
            - sed -i "s|{{client_base_url}}|$CLIENT_BASE_URL_DEV|g" Dockerfile
            - IMAGE="${ECR_IMAGE_DEV}"
            - TAG="${BITBUCKET_BUILD_NUMBER}"
            - docker build -t $IMAGE:$TAG .
            - pipe: atlassian/aws-ecr-push-image:2.0.0
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
                AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION_DEV}
                IMAGE_NAME: "${IMAGE}"
                TAGS: "${TAG}"
                DEBUG: "true"
      - step:
          name: "Deploy to dev environment"
          deployment: dev
          script:
            - sed -i "s|{{image}}|$ECR_REGISTRY_DEV/$ECR_IMAGE_DEV:$BITBUCKET_BUILD_NUMBER|g" deployment.yml
            - pipe: atlassian/aws-eks-kubectl-run:2.2.1
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
                AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION_DEV}
                CLUSTER_NAME: "${VENTUREC_CLUSTER_DEV}"
                KUBECTL_COMMAND: "apply"
                RESOURCE_PATH: "deployment.yml"
                KUBECTL_ARGS:
                  - "--namespace=${VENTUREC_NAMESPACE_DEV}"
                DEBUG: "true"
    master:
      - parallel:
          - step:
              name: Build and Test
              caches:
                - node
              script:
                - rm -rf package-lock.json
                - rm -rf node_modules
                - npm install --unsafe-perm=true --allow-root cypress
                - npm install --production
                - npm run build
          - step:
              name: Security Scan
              script:
                - pipe: atlassian/git-secrets-scan:0.4.3
      - step:
          name: Build Docker Image and Push to Container Registry
          services:
            - docker
          size: 2x
          script:
            - sed -i "s|{{rest_api_url}}|$REST_API_URL_PROD|g" Dockerfile
            - sed -i "s|{{keycloak_url}}|$KEYCLOAK_URL_PROD|g" Dockerfile
            - sed -i "s|{{client_base_url}}|$CLIENT_BASE_URL_PROD|g" Dockerfile
            - IMAGE="${ECR_IMAGE_PROD}"
            - TAG="${BITBUCKET_BUILD_NUMBER}"
            - docker build -t $IMAGE:$TAG .
            - pipe: atlassian/aws-ecr-push-image:2.0.0
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
                AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION_PROD}
                IMAGE_NAME: "${IMAGE}"
                TAGS: "${TAG}"
                DEBUG: "true"
      - step:
          name: "Deploy to prod environment"
          deployment: prod
          script:
            - sed -i "s|{{image}}|$ECR_REGISTRY_PROD/$ECR_IMAGE_PROD:$BITBUCKET_BUILD_NUMBER|g" deployment.yml
            - pipe: atlassian/aws-eks-kubectl-run:2.2.1
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
                AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION_PROD}
                CLUSTER_NAME: "${VENTUREC_CLUSTER_PROD}"
                KUBECTL_COMMAND: "apply"
                RESOURCE_PATH: "deployment.yml"
                KUBECTL_ARGS:
                  - "--namespace=${VENTUREC_NAMESPACE_PROD}"
                DEBUG: "true"
definitions:
  services:
    docker:
      memory: 4096
