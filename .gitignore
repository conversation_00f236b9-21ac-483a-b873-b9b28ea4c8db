# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
tsconfig.tsbuildinfo


node_modules
dist
dist-ssr
*.local

.editorconfig

# testing
/coverage
/cypress/videos
/cypress/screenshots

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?


HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!*/src/main/*/target/
!*/src/test/*/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!*/src/main/*/build/
!*/src/test/*/build/

*.env


# typescript
*.tsbuildinfo
.qodo
.vscode
.claude