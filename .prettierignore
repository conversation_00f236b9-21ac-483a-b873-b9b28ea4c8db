# Dependencies
node_modules/

# Build outputs
dist/
build/
.next/
out/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# ESLint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# dotenv environment variables files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Package files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Auto-generated files
*.d.ts

# Storybook
.out
.storybook-out
storybook-static/

# Public assets (images, fonts, etc.)
public/
src/assets/

# Config files that should maintain their specific formatting
*.config.js
*.config.ts

# Deployment files
Dockerfile
docker-compose.yml
docker-compose.*.yml
deployment.yml
bitbucket-pipelines.yml