/** @type {import('tailwindcss').Config} */
const config = {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: 'hsl(var(--primary)/<alpha-value>)',
        secondary: 'var(--secondary)',
        black: 'hsl(var(--black)/<alpha-value>)',
        blackOne: 'var(--blackOne)',
        blackTwo: 'var(--blackTwo)',
        blackThree: 'var(--blackThree)',
        blackFour: 'var(--blackFour)',
        blackFive: 'var(--blackFive)',
        subText: 'var(--subText)',
        white: 'var(--white)',
        'white-opaque': 'hsl(var(--white-opaque)/<alpha-value>)',
        whiteOff: 'var(--whiteOff)',
        green: 'var(--green)',
        greenOne: 'var(--greenOne)',
        greenTwo: 'var(--greenTwo)',
        greenFade: 'var(--greenFade)',
        darkOrangeTwo: 'var(--dark-orangeTwo)',
        orangeTwo: 'hsl(var(--orangeTwo)/<alpha-value>)',
        orangeTwoFade: 'var(--orangeTwoFade)',
        orangeThreeFade: 'var(--orangeThreeFade)',
        orangeFour: 'var(--orangeFour)',
        orangeThree: 'var(--orangeThree)',
        orangeFourFade: 'var(--orangeFourFade)',
        peachOne: 'var(--peachOne)',
        peachTwo: 'var(--peachTwo)',
        purple: 'var(--purple)',
        purpleOne: 'var(--purpleOne)',
        purpleFade: 'var(--purpleFade)',
        purpleTwoFade: 'var(--purpleTwoFade)',
        yellowFade: 'var(--yellowFade)',
        yellow: 'var(--yellow)',
        yellowOne: 'var(--yellowOne)',
        graySeven: 'var(--graySeven)',
        grayNine: 'var(--grayNine)',
        grayTen: 'var(--grayTen)',
        grayEleven: 'var(--grayEleven)',
        grayTwelve: 'var(--grayTwelve)',
        grayThirteen: 'var(--grayThirteen)',
        grayFourteen: 'var(--grayFourteen)',
        grayFifteen: 'var(--grayFifteen)',
        graySixteen: 'var(--graySixteen)',
        graySeventeen: 'var(--graySeventeen)',
        grayEigtheen: 'var(--grayEigtheen)',
        grayNineTeen: 'var(--grayNineTeen)',
        grayTwenty: 'var(--grayTwenty)',
        grayTwentyOne: 'var(--grayTwentyOne)',
        grayTwentyTwo: 'var(--grayTwentyTwo)',
        grayTwentyThree: 'var(--grayTwentyThree)',
        grayTwentyFour: 'var(--grayTwentyFour)',
        grayTwentyFive: 'var(--grayTwentyFive)',
        grayTwentySix: 'var(--grayTwentySix)',
        grayTwentySeven: 'var(--grayTwentySeven)',
        grayTwentyEight: 'var(--grayTwentyEight)',
        grayTwo: 'var(--grayTwo)',
        grayOne: 'var(--grayOne)',
        lightBrown: 'var(--lightBrown)',
        lightPurple: 'var(--lightPurple)',
        lightGreen: 'var(--lightGreen)',
        greenThree: 'var(--greenThree)',
        brown: 'var(--brown)',
        lightOrangeTwo: 'var(--light-orangeTwo)',
        dangerTwo: 'var(--dangerTwo)',
        successTwo: 'var(--successTwo)',
        lightYellow: 'var(--lightYellow)',
        lightYellowTwo: 'var(--lightYellowTwo)',
        blue: {
          midnight: 'var(--midnight-blue)',
        },
        blueGray: 'var(--blueGray)',
        blueOne: 'var(--blueOne)',
        blueTwo: 'var(--blueTwo)',
        darkGray: 'var(--dark-gray)',
        darkBlue: 'var(--darkBlue)',
        darkBlueOne: 'var(--darkBlueOne)',
        linkLightBlue: 'var(--linkLightBlue)',
        gray: {
          0: '#F9FAFC',
          5: '#F4F5F7',
          10: '#F4EFEB',
          15: '#AAADB7',
          20: '#838E9A',
          25: '#AEAEAE',
          30: '#6E6E6E',
          35: 'rgba(28,28,28,0.3)',
          40: '#6E6E6E',
        },
        orange: {
          5: 'var(--light-orange)',
          10: 'var(--orange)',
          15: 'var(--dark-orange)',
        },
        peach: {
          5: 'var(--light-peach)',
        },
        success: 'var(--success)',
        danger: 'var(--danger)',
        warning: 'var(--warning)',
        disabled: 'var(--disabled)',
        alert: 'var(--alert)',
        delete: 'var(--delete)',
      },
      fontFamily: {
        spartan: ["'Spartan'", 'sans-serif'],
        'space-mono': ["'Space Mono'", 'monospace'],
        inter: ["'Inter'", 'sans-serif'],
      },
      screens: {
        'xs': '475px',
        '2xl': '1440px',
        '3xl': '1920px',
      },
      animation: {
        'slide-down': 'slideDown 0.3s ease-out',
        'fade-in': 'fadeIn 0.2s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'scroll-right': 'scroll-right 15s linear infinite',
      },
      keyframes: {
        slideDown: {
          '0%': { transform: 'translateY(-100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        'scroll-right': {
          '0%': { transform: 'translateX(-33.333%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
    },
  },
  plugins: [require('@tailwindcss/forms'), require('tailwind-scrollbar')({ nocompatible: true })],
};

export default config;